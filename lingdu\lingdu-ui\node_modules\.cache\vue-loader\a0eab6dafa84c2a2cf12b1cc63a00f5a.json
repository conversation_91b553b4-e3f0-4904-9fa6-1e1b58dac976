{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\layout\\components\\Navbar.vue?vue&type=style&index=0&id=d16d6306&lang=scss&scoped=true", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\layout\\components\\Navbar.vue", "mtime": 1758178462816}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757926224263}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757926225750}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757926224811}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757926223774}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <!-- 头部导航 -->\r\n  <el-header class=\"header\" height=\"64px\">\r\n    <el-row type=\"flex\" justify=\"space-between\" align=\"middle\" class=\"header-row\">\r\n      <!-- Logo和标题 -->\r\n      <el-col :span=\"6\">\r\n        <div class=\"logo-section\">\r\n          <div class=\"logo\">\r\n            <i class=\"el-icon-cpu\"></i>\r\n          </div>\r\n          <h1 class=\"title\">灵渡AI学习助手</h1>\r\n        </div>\r\n      </el-col>\r\n\r\n      <!-- 导航链接 -->\r\n      <el-col :span=\"12\" class=\"nav-col\">\r\n        <el-menu mode=\"horizontal\" :default-active=\"activeIndex\" class=\"nav-menu\" @select=\"handleSelect\">\r\n          <el-menu-item index=\"home\">首页</el-menu-item>\r\n          <el-menu-item index=\"about\">关于我们</el-menu-item>\r\n          <el-menu-item index=\"help\">帮助</el-menu-item>\r\n        </el-menu>\r\n      </el-col>\r\n\r\n      <!-- 登录注册 / 退出 -->\r\n      <el-col :span=\"6\" class=\"auth-col\">\r\n        <div class=\"auth-buttons\">\r\n          <el-button v-if=\"isLoggedIn\" @click=\"goProfile\">\r\n            个人中心\r\n          </el-button>\r\n          <el-button v-if=\"!isLoggedIn\" class=\"login-btn\" @click=\"handleLogin\" :loading=\"loginLoading\">\r\n            登录\r\n          </el-button>\r\n          <el-button v-if=\"!isLoggedIn\" type=\"primary\" @click=\"handleRegister\" :loading=\"registerLoading\">\r\n            注册\r\n          </el-button>\r\n          <el-button v-else type=\"danger\" @click=\"logout\">\r\n            退出\r\n          </el-button>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </el-header>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Breadcrumb from '@/components/Breadcrumb'\r\nimport TopNav from '@/components/TopNav'\r\nimport Hamburger from '@/components/Hamburger'\r\nimport Screenfull from '@/components/Screenfull'\r\nimport SizeSelect from '@/components/SizeSelect'\r\nimport Search from '@/components/HeaderSearch'\r\nimport RuoYiGit from '@/components/RuoYi/Git'\r\nimport RuoYiDoc from '@/components/RuoYi/Doc'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  emits: ['setLayout'],\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    RuoYiGit,\r\n    RuoYiDoc\r\n  },\r\n  data() {\r\n    return {\r\n      activeIndex: 'home',\r\n      loginLoading: false,\r\n      registerLoading: false,\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebar',\r\n      'avatar',\r\n      'device',\r\n      'nickName'\r\n    ]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      }\r\n    },\r\n    isLoggedIn() {\r\n      return !!getToken()\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.$store.dispatch('app/toggleSideBar')\r\n    },\r\n    setLayout(event) {\r\n      this.$emit('setLayout')\r\n    },\r\n    handleSelect(key) {\r\n      this.activeIndex = key\r\n      switch (key) {\r\n        case 'home':\r\n          this.$router.push('/')\r\n          break\r\n        case 'about':\r\n          this.$router.push('/about')\r\n          break\r\n        case 'help':\r\n          // 如果存在帮助路由则跳转，否则给出提示\r\n          const hasHelp = this.$router.options.routes.some(r => r.path === '/help')\r\n          if (hasHelp) {\r\n            this.$router.push('/help')\r\n          } else {\r\n            this.$message('帮助页面开发中...')\r\n          }\r\n          break\r\n      }\r\n    },\r\n    handleLogin() {\r\n      this.loginLoading = true\r\n      const hasToken = this.$store.getters.token || this.$store.getters.roles?.length > 0\r\n      const goLogin = () => {\r\n        this.loginLoading = false\r\n        this.$router.replace('/login')\r\n      }\r\n      if (hasToken) {\r\n        // 清除登录态再进入登录页，避免被路由守卫重定向回首页\r\n        this.$store.dispatch('LogOut').finally(goLogin)\r\n      } else {\r\n        goLogin()\r\n      }\r\n    },\r\n    handleRegister() {\r\n      this.registerLoading = true\r\n      setTimeout(() => {\r\n        this.registerLoading = false\r\n        this.$router.push('/register')\r\n      }, 1000)\r\n    },\r\n    logout() {\r\n      this.$confirm('确定退出系统吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$store.dispatch('LogOut').then(() => {\r\n          location.href = '/index'\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    goProfile() {\r\n      this.$router.push('/user/profile')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.navbar {\r\n  height: 50px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);\r\n\r\n  .hamburger-container {\r\n    line-height: 46px;\r\n    height: 100%;\r\n    float: left;\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n    -webkit-tap-highlight-color: transparent;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .025)\r\n    }\r\n  }\r\n\r\n  .breadcrumb-container {\r\n    float: left;\r\n  }\r\n\r\n  .topmenu-container {\r\n    position: absolute;\r\n    left: 50px;\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .right-menu {\r\n    float: right;\r\n    height: 100%;\r\n    line-height: 50px;\r\n\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background .3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, .025)\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      margin-right: 0px;\r\n      padding-right: 0px;\r\n\r\n      .avatar-wrapper {\r\n        margin-top: 10px;\r\n        right: 8px;\r\n        position: relative;\r\n\r\n        .user-avatar {\r\n          cursor: pointer;\r\n          width: 30px;\r\n          height: 30px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .user-nickname {\r\n          position: relative;\r\n          bottom: 10px;\r\n          left: 2px;\r\n          font-size: 14px;\r\n          font-weight: bold;\r\n        }\r\n\r\n        .el-icon-caret-bottom {\r\n          cursor: pointer;\r\n          position: absolute;\r\n          right: -20px;\r\n          top: 25px;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n/* Header navbar styles */\r\n.header {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 1000;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.header-row {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.logo {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 20px;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin: 0;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.nav-col {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.nav-menu {\r\n  border-bottom: none;\r\n  background: transparent;\r\n}\r\n\r\n.nav-menu .el-menu-item {\r\n  color: #606266;\r\n  font-weight: 500;\r\n  border-bottom: 2px solid transparent;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-menu .el-menu-item:hover,\r\n.nav-menu .el-menu-item.is-active {\r\n  color: #409EFF;\r\n  border-bottom-color: #409EFF;\r\n  background: transparent;\r\n}\r\n\r\n.auth-col {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.auth-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.login-btn {\r\n  border: 1px solid #409EFF;\r\n  color: #409EFF;\r\n  background: transparent;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.login-btn:hover {\r\n  background-color: #409EFF;\r\n  color: white;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\r\n}\r\n</style>\r\n"]}]}