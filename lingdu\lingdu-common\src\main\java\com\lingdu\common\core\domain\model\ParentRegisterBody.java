package com.lingdu.common.core.domain.model;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 家长注册请求体
 * 
 * <AUTHOR>
 */
public class ParentRegisterBody extends RegisterBody
{
    /** 真实姓名 */
    @NotBlank(message = "真实姓名不能为空")
    @Size(min = 2, max = 30, message = "真实姓名长度必须在2到30个字符之间")
    private String realName;

    /** 权限级别 */
    private String permissionLevel = "view_only";

    public String getRealName()
    {
        return realName;
    }

    public void setRealName(String realName)
    {
        this.realName = realName;
    }

    public String getPermissionLevel()
    {
        return permissionLevel;
    }

    public void setPermissionLevel(String permissionLevel)
    {
        this.permissionLevel = permissionLevel;
    }
}
