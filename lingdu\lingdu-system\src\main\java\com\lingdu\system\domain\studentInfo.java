package com.lingdu.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lingdu.common.annotation.Excel;
import com.lingdu.common.core.domain.BaseEntity;

/**
 * 学生信息对象 student_info
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
public class studentInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 学生ID */
    private Long studentId;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickName;

    /** 用户显示名称 */
    private String userDisplayName;

    /** 年级 */
    @Excel(name = "年级")
    private String grade;

    /** 学段 */
    @Excel(name = "学段")
    private String stage;

    /** 主学科 */
    @Excel(name = "主学科")
    private String mainSubject;

    /** 副学科 */
    private String extraSubjects;

    /** 会员类型 */
    @Excel(name = "会员类型")
    private String membershipType;

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getNickName()
    {
        return nickName;
    }

    public void setUserDisplayName(String userDisplayName) 
    {
        this.userDisplayName = userDisplayName;
    }

    public String getUserDisplayName() 
    {
        return userDisplayName;
    }

    public void setGrade(String grade) 
    {
        this.grade = grade;
    }

    public String getGrade() 
    {
        return grade;
    }

    public void setStage(String stage) 
    {
        this.stage = stage;
    }

    public String getStage() 
    {
        return stage;
    }

    public void setMainSubject(String mainSubject) 
    {
        this.mainSubject = mainSubject;
    }

    public String getMainSubject() 
    {
        return mainSubject;
    }

    public void setExtraSubjects(String extraSubjects) 
    {
        this.extraSubjects = extraSubjects;
    }

    public String getExtraSubjects() 
    {
        return extraSubjects;
    }

    public void setMembershipType(String membershipType) 
    {
        this.membershipType = membershipType;
    }

    public String getMembershipType() 
    {
        return membershipType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("studentId", getStudentId())
            .append("nickName", getNickName())
            .append("userDisplayName", getUserDisplayName())
            .append("grade", getGrade())
            .append("stage", getStage())
            .append("mainSubject", getMainSubject())
            .append("extraSubjects", getExtraSubjects())
            .append("membershipType", getMembershipType())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
