-- ========================================
-- 教育系统多角色数据库扩展方案
-- 根据需求文档设计的完整数据库架构
-- ========================================

-- 1. 扩展用户类型字典数据
INSERT INTO sys_dict_type VALUES(100, '用户类型', 'sys_user_type', '0', 'admin', sysdate(), '', null, '用户类型列表');
INSERT INTO sys_dict_type VALUES(101, '学段类型', 'edu_grade_level', '0', 'admin', sysdate(), '', null, '学段类型列表');
INSERT INTO sys_dict_type VALUES(102, '学科类型', 'edu_subject', '0', 'admin', sysdate(), '', null, '学科类型列表');
INSERT INTO sys_dict_type VALUES(103, '会员类型', 'edu_member_type', '0', 'admin', sysdate(), '', null, '会员类型列表');
INSERT INTO sys_dict_type VALUES(104, '审核状态', 'audit_status', '0', 'admin', sysdate(), '', null, '审核状态列表');
INSERT INTO sys_dict_type VALUES(105, '关联权限', 'parent_permission', '0', 'admin', sysdate(), '', null, '家长权限列表');

-- 用户类型字典数据
INSERT INTO sys_dict_data VALUES(100, 1, '系统用户', '00', 'sys_user_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '系统管理用户');
INSERT INTO sys_dict_data VALUES(101, 2, '学生', '01', 'sys_user_type', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '学生用户');
INSERT INTO sys_dict_data VALUES(102, 3, '家长', '02', 'sys_user_type', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '家长用户');
INSERT INTO sys_dict_data VALUES(103, 4, '教师', '03', 'sys_user_type', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '教师用户');

-- 学段类型字典数据
INSERT INTO sys_dict_data VALUES(110, 1, '幼升小', 'pre_primary', 'edu_grade_level', '', 'default', 'Y', '0', 'admin', sysdate(), '', null, '幼升小');
INSERT INTO sys_dict_data VALUES(111, 2, '小学1年级', 'primary_1', 'edu_grade_level', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '小学1年级');
INSERT INTO sys_dict_data VALUES(112, 3, '小学2年级', 'primary_2', 'edu_grade_level', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '小学2年级');
INSERT INTO sys_dict_data VALUES(113, 4, '小学3年级', 'primary_3', 'edu_grade_level', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '小学3年级');
INSERT INTO sys_dict_data VALUES(114, 5, '小学4年级', 'primary_4', 'edu_grade_level', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '小学4年级');
INSERT INTO sys_dict_data VALUES(115, 6, '小学5年级', 'primary_5', 'edu_grade_level', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '小学5年级');
INSERT INTO sys_dict_data VALUES(116, 7, '小学6年级', 'primary_6', 'edu_grade_level', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '小学6年级');
INSERT INTO sys_dict_data VALUES(117, 8, '初中1年级', 'junior_1', 'edu_grade_level', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '初中1年级');
INSERT INTO sys_dict_data VALUES(118, 9, '初中2年级', 'junior_2', 'edu_grade_level', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '初中2年级');
INSERT INTO sys_dict_data VALUES(119, 10, '初中3年级', 'junior_3', 'edu_grade_level', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '初中3年级');
INSERT INTO sys_dict_data VALUES(120, 11, '高中1年级', 'senior_1', 'edu_grade_level', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '高中1年级');
INSERT INTO sys_dict_data VALUES(121, 12, '高中2年级', 'senior_2', 'edu_grade_level', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '高中2年级');
INSERT INTO sys_dict_data VALUES(122, 13, '高中3年级', 'senior_3', 'edu_grade_level', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '高中3年级');

-- 学科类型字典数据
INSERT INTO sys_dict_data VALUES(130, 1, '语文', 'chinese', 'edu_subject', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '语文');
INSERT INTO sys_dict_data VALUES(131, 2, '数学', 'math', 'edu_subject', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '数学');
INSERT INTO sys_dict_data VALUES(132, 3, '英语', 'english', 'edu_subject', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '英语');
INSERT INTO sys_dict_data VALUES(133, 4, '物理', 'physics', 'edu_subject', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '物理');
INSERT INTO sys_dict_data VALUES(134, 5, '化学', 'chemistry', 'edu_subject', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '化学');
INSERT INTO sys_dict_data VALUES(135, 6, '生物', 'biology', 'edu_subject', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '生物');
INSERT INTO sys_dict_data VALUES(136, 7, '历史', 'history', 'edu_subject', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '历史');
INSERT INTO sys_dict_data VALUES(137, 8, '地理', 'geography', 'edu_subject', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '地理');
INSERT INTO sys_dict_data VALUES(138, 9, '政治', 'politics', 'edu_subject', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '政治');

-- 会员类型字典数据
INSERT INTO sys_dict_data VALUES(140, 1, '免费体验', 'free_trial', 'edu_member_type', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '免费体验7天');
INSERT INTO sys_dict_data VALUES(141, 2, '基础会员', 'basic', 'edu_member_type', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '基础会员');
INSERT INTO sys_dict_data VALUES(142, 3, 'VIP会员', 'vip', 'edu_member_type', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, 'VIP会员');
INSERT INTO sys_dict_data VALUES(143, 4, '超级VIP', 'super_vip', 'edu_member_type', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '超级VIP会员');

-- 审核状态字典数据
INSERT INTO sys_dict_data VALUES(150, 1, '待审核', 'pending', 'audit_status', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '待审核');
INSERT INTO sys_dict_data VALUES(151, 2, '审核通过', 'approved', 'audit_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '审核通过');
INSERT INTO sys_dict_data VALUES(152, 3, '审核拒绝', 'rejected', 'audit_status', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '审核拒绝');

-- 家长权限字典数据
INSERT INTO sys_dict_data VALUES(160, 1, '仅查看学习进度', 'view_only', 'parent_permission', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '仅查看学习进度');
INSERT INTO sys_dict_data VALUES(161, 2, '查看+接收通知', 'view_notify', 'parent_permission', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '查看学习进度并接收通知');
INSERT INTO sys_dict_data VALUES(162, 3, '查看+沟通老师', 'view_communicate', 'parent_permission', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '查看学习进度并可与老师沟通');

-- 2. 创建学生信息扩展表
DROP TABLE IF EXISTS edu_student_info;
CREATE TABLE edu_student_info (
  student_id           bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '学生信息ID',
  user_id              bigint(20)      NOT NULL                   COMMENT '关联用户ID',
  student_number       varchar(50)     DEFAULT ''                 COMMENT '学号',
  real_name            varchar(30)     NOT NULL                   COMMENT '真实姓名',
  id_card              varchar(18)     DEFAULT ''                 COMMENT '身份证号',
  grade_level          varchar(20)     NOT NULL                   COMMENT '学段（对应字典edu_grade_level）',
  primary_subject      varchar(20)     NOT NULL                   COMMENT '主学科（对应字典edu_subject）',
  secondary_subjects   varchar(200)    DEFAULT ''                 COMMENT '副学科，逗号分隔（对应字典edu_subject）',
  member_type          varchar(20)     DEFAULT 'free_trial'       COMMENT '会员类型（对应字典edu_member_type）',
  member_expire_date   datetime                                   COMMENT '会员到期时间',
  school_name          varchar(100)    DEFAULT ''                 COMMENT '学校名称',
  class_name           varchar(50)     DEFAULT ''                 COMMENT '班级名称',
  parent_phone         varchar(11)     DEFAULT ''                 COMMENT '家长手机号',
  status               char(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
  create_by            varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time          datetime                                   COMMENT '创建时间',
  update_by            varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time          datetime                                   COMMENT '更新时间',
  remark               varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (student_id),
  UNIQUE KEY uk_user_id (user_id),
  UNIQUE KEY uk_student_number (student_number),
  INDEX idx_grade_level (grade_level),
  INDEX idx_primary_subject (primary_subject),
  INDEX idx_parent_phone (parent_phone)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '学生信息扩展表';

-- 3. 创建教师认证信息表
DROP TABLE IF EXISTS edu_teacher_info;
CREATE TABLE edu_teacher_info (
  teacher_id              bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '教师信息ID',
  user_id                 bigint(20)      NOT NULL                   COMMENT '关联用户ID',
  real_name               varchar(30)     NOT NULL                   COMMENT '真实姓名',
  id_card                 varchar(18)     DEFAULT ''                 COMMENT '身份证号',
  teacher_cert_number     varchar(50)     NOT NULL                   COMMENT '教师资格证编号',
  teacher_cert_image      varchar(200)    DEFAULT ''                 COMMENT '教师资格证照片路径',
  teaching_grade_levels   varchar(100)    NOT NULL                   COMMENT '教学学段，逗号分隔（对应字典edu_grade_level）',
  teaching_subjects       varchar(100)    NOT NULL                   COMMENT '教学学科，逗号分隔（对应字典edu_subject）',
  school_name             varchar(100)    DEFAULT ''                 COMMENT '任职学校',
  audit_status            varchar(20)     DEFAULT 'pending'          COMMENT '审核状态（对应字典audit_status）',
  audit_by                varchar(64)     DEFAULT ''                 COMMENT '审核人',
  audit_time              datetime                                   COMMENT '审核时间',
  audit_remark            varchar(500)    DEFAULT ''                 COMMENT '审核备注',
  status                  char(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
  create_by               varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time             datetime                                   COMMENT '创建时间',
  update_by               varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time             datetime                                   COMMENT '更新时间',
  remark                  varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (teacher_id),
  UNIQUE KEY uk_user_id (user_id),
  UNIQUE KEY uk_teacher_cert (teacher_cert_number),
  INDEX idx_audit_status (audit_status),
  INDEX idx_teaching_subjects (teaching_subjects)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '教师认证信息表';

-- 4. 创建家长学生关联表
DROP TABLE IF EXISTS edu_parent_student;
CREATE TABLE edu_parent_student (
  relation_id          bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '关联ID',
  parent_user_id       bigint(20)      NOT NULL                   COMMENT '家长用户ID',
  student_user_id      bigint(20)      NOT NULL                   COMMENT '学生用户ID',
  relation_type        varchar(20)     DEFAULT 'parent'           COMMENT '关系类型（parent-父母，guardian-监护人）',
  permission_level     varchar(20)     DEFAULT 'view_only'        COMMENT '权限级别（对应字典parent_permission）',
  verify_code          varchar(10)     DEFAULT ''                 COMMENT '关联验证码',
  verify_status        char(1)         DEFAULT '0'                COMMENT '验证状态（0待验证 1已验证）',
  verify_time          datetime                                   COMMENT '验证时间',
  status               char(1)         DEFAULT '0'                COMMENT '关联状态（0正常 1停用）',
  create_by            varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time          datetime                                   COMMENT '创建时间',
  update_by            varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time          datetime                                   COMMENT '更新时间',
  remark               varchar(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (relation_id),
  UNIQUE KEY uk_parent_student (parent_user_id, student_user_id),
  INDEX idx_parent_user (parent_user_id),
  INDEX idx_student_user (student_user_id),
  INDEX idx_verify_status (verify_status)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '家长学生关联表';

-- 5. 创建用户身份切换记录表
DROP TABLE IF EXISTS edu_user_role_switch;
CREATE TABLE edu_user_role_switch (
  switch_id            bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '切换记录ID',
  user_id              bigint(20)      NOT NULL                   COMMENT '用户ID',
  from_role            varchar(20)     DEFAULT ''                 COMMENT '切换前角色',
  to_role              varchar(20)     NOT NULL                   COMMENT '切换后角色',
  context_data         text                                       COMMENT '上下文数据（JSON格式）',
  switch_time          datetime        NOT NULL                   COMMENT '切换时间',
  ip_address           varchar(128)    DEFAULT ''                 COMMENT 'IP地址',
  user_agent           varchar(500)    DEFAULT ''                 COMMENT '用户代理',
  PRIMARY KEY (switch_id),
  INDEX idx_user_id (user_id),
  INDEX idx_switch_time (switch_time)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '用户身份切换记录表';

-- 6. 创建短信验证码记录表
DROP TABLE IF EXISTS edu_sms_verify;
CREATE TABLE edu_sms_verify (
  verify_id            bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '验证ID',
  phone_number         varchar(11)     NOT NULL                   COMMENT '手机号码',
  verify_code          varchar(10)     NOT NULL                   COMMENT '验证码',
  verify_type          varchar(20)     NOT NULL                   COMMENT '验证类型（register-注册，login-登录，bind-绑定）',
  user_type            varchar(2)      DEFAULT ''                 COMMENT '用户类型',
  used_status          char(1)         DEFAULT '0'                COMMENT '使用状态（0未使用 1已使用）',
  expire_time          datetime        NOT NULL                   COMMENT '过期时间',
  create_time          datetime        NOT NULL                   COMMENT '创建时间',
  use_time             datetime                                   COMMENT '使用时间',
  ip_address           varchar(128)    DEFAULT ''                 COMMENT 'IP地址',
  PRIMARY KEY (verify_id),
  INDEX idx_phone_code (phone_number, verify_code),
  INDEX idx_expire_time (expire_time),
  INDEX idx_verify_type (verify_type)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '短信验证码记录表';

-- 7. 创建角色和权限配置
-- 学生角色
INSERT INTO sys_role VALUES(100, '学生', 'student', 100, '5', 1, 1, '0', '0', 'admin', sysdate(), '', null, '学生角色');

-- 家长角色
INSERT INTO sys_role VALUES(101, '家长', 'parent', 101, '5', 1, 1, '0', '0', 'admin', sysdate(), '', null, '家长角色');

-- 教师角色
INSERT INTO sys_role VALUES(102, '教师', 'teacher', 102, '4', 1, 1, '0', '0', 'admin', sysdate(), '', null, '教师角色');

-- 8. 创建菜单权限配置
-- 个人中心菜单
INSERT INTO sys_menu VALUES(2000, '个人中心', 0, 1, 'profile', null, null, 1, 0, 'M', '0', '0', '', 'user', 'admin', sysdate(), '', null, '个人中心目录');
INSERT INTO sys_menu VALUES(2001, '个人信息', 2000, 1, 'info', 'system/user/profile/index', null, 1, 0, 'C', '0', '0', 'system:user:profile', 'user', 'admin', sysdate(), '', null, '个人信息菜单');
INSERT INTO sys_menu VALUES(2002, '修改密码', 2000, 2, 'password', 'system/user/profile/resetPwd', null, 1, 0, 'C', '0', '0', 'system:user:profile', 'password', 'admin', sysdate(), '', null, '修改密码菜单');

-- 学生专用菜单
INSERT INTO sys_menu VALUES(3000, '学习中心', 0, 2, 'study', null, null, 1, 0, 'M', '0', '0', '', 'education', 'admin', sysdate(), '', null, '学习中心目录');
INSERT INTO sys_menu VALUES(3001, '我的课程', 3000, 1, 'courses', 'student/courses/index', null, 1, 0, 'C', '0', '0', 'student:courses:list', 'skill', 'admin', sysdate(), '', null, '我的课程菜单');
INSERT INTO sys_menu VALUES(3002, '学习计划', 3000, 2, 'plan', 'student/plan/index', null, 1, 0, 'C', '0', '0', 'student:plan:view', 'date', 'admin', sysdate(), '', null, '学习计划菜单');
INSERT INTO sys_menu VALUES(3003, '我的成绩', 3000, 3, 'grades', 'student/grades/index', null, 1, 0, 'C', '0', '0', 'student:grades:list', 'chart', 'admin', sysdate(), '', null, '我的成绩菜单');
INSERT INTO sys_menu VALUES(3004, '作业提交', 3000, 4, 'homework', 'student/homework/index', null, 1, 0, 'C', '0', '0', 'student:homework:submit', 'edit', 'admin', sysdate(), '', null, '作业提交菜单');
INSERT INTO sys_menu VALUES(3005, '学科切换', 3000, 5, 'subject-switch', 'student/subject/switch', null, 1, 0, 'C', '0', '0', 'student:subject:switch', 'switch', 'admin', sysdate(), '', null, '学科切换菜单');

-- 家长专用菜单
INSERT INTO sys_menu VALUES(4000, '家长中心', 0, 3, 'parent', null, null, 1, 0, 'M', '0', '0', '', 'peoples', 'admin', sysdate(), '', null, '家长中心目录');
INSERT INTO sys_menu VALUES(4001, '关联学生', 4000, 1, 'bind-student', 'parent/bind/index', null, 1, 0, 'C', '0', '0', 'parent:bind:student', 'link', 'admin', sysdate(), '', null, '关联学生菜单');
INSERT INTO sys_menu VALUES(4002, '孩子信息', 4000, 2, 'children', 'parent/children/index', null, 1, 0, 'C', '0', '0', 'parent:children:list', 'user', 'admin', sysdate(), '', null, '孩子信息菜单');
INSERT INTO sys_menu VALUES(4003, '学习报告', 4000, 3, 'reports', 'parent/reports/index', null, 1, 0, 'C', '0', '0', 'parent:reports:view', 'documentation', 'admin', sysdate(), '', null, '学习报告菜单');
INSERT INTO sys_menu VALUES(4004, '家校沟通', 4000, 4, 'communication', 'parent/communication/index', null, 1, 0, 'C', '0', '0', 'parent:communication:view', 'message', 'admin', sysdate(), '', null, '家校沟通菜单');
INSERT INTO sys_menu VALUES(4005, '身份切换', 4000, 5, 'role-switch', 'parent/switch/index', null, 1, 0, 'C', '0', '0', 'parent:role:switch', 'switch', 'admin', sysdate(), '', null, '身份切换菜单');

-- 教师专用菜单
INSERT INTO sys_menu VALUES(5000, '教师中心', 0, 4, 'teacher', null, null, 1, 0, 'M', '0', '0', '', 'education', 'admin', sysdate(), '', null, '教师中心目录');
INSERT INTO sys_menu VALUES(5001, '我的班级', 5000, 1, 'classes', 'teacher/classes/index', null, 1, 0, 'C', '0', '0', 'teacher:classes:manage', 'peoples', 'admin', sysdate(), '', null, '班级管理菜单');
INSERT INTO sys_menu VALUES(5002, '课程管理', 5000, 2, 'courses', 'teacher/courses/index', null, 1, 0, 'C', '0', '0', 'teacher:courses:manage', 'skill', 'admin', sysdate(), '', null, '课程管理菜单');
INSERT INTO sys_menu VALUES(5003, '成绩管理', 5000, 3, 'grades', 'teacher/grades/index', null, 1, 0, 'C', '0', '0', 'teacher:grades:manage', 'chart', 'admin', sysdate(), '', null, '成绩管理菜单');
INSERT INTO sys_menu VALUES(5004, '作业管理', 5000, 4, 'homework', 'teacher/homework/index', null, 1, 0, 'C', '0', '0', 'teacher:homework:manage', 'edit', 'admin', sysdate(), '', null, '作业管理菜单');
INSERT INTO sys_menu VALUES(5005, '学生管理', 5000, 5, 'students', 'teacher/students/index', null, 1, 0, 'C', '0', '0', 'teacher:students:manage', 'user', 'admin', sysdate(), '', null, '学生管理菜单');

-- 9. 角色菜单权限分配
-- 学生角色权限
INSERT INTO sys_role_menu VALUES (100, 2000); -- 个人中心
INSERT INTO sys_role_menu VALUES (100, 2001); -- 个人信息
INSERT INTO sys_role_menu VALUES (100, 2002); -- 修改密码
INSERT INTO sys_role_menu VALUES (100, 3000); -- 学习中心
INSERT INTO sys_role_menu VALUES (100, 3001); -- 我的课程
INSERT INTO sys_role_menu VALUES (100, 3002); -- 学习计划
INSERT INTO sys_role_menu VALUES (100, 3003); -- 我的成绩
INSERT INTO sys_role_menu VALUES (100, 3004); -- 作业提交
INSERT INTO sys_role_menu VALUES (100, 3005); -- 学科切换

-- 家长角色权限
INSERT INTO sys_role_menu VALUES (101, 2000); -- 个人中心
INSERT INTO sys_role_menu VALUES (101, 2001); -- 个人信息
INSERT INTO sys_role_menu VALUES (101, 2002); -- 修改密码
INSERT INTO sys_role_menu VALUES (101, 4000); -- 家长中心
INSERT INTO sys_role_menu VALUES (101, 4001); -- 关联学生
INSERT INTO sys_role_menu VALUES (101, 4002); -- 孩子信息
INSERT INTO sys_role_menu VALUES (101, 4003); -- 学习报告
INSERT INTO sys_role_menu VALUES (101, 4004); -- 家校沟通
INSERT INTO sys_role_menu VALUES (101, 4005); -- 身份切换

-- 教师角色权限
INSERT INTO sys_role_menu VALUES (102, 2000); -- 个人中心
INSERT INTO sys_role_menu VALUES (102, 2001); -- 个人信息
INSERT INTO sys_role_menu VALUES (102, 2002); -- 修改密码
INSERT INTO sys_role_menu VALUES (102, 5000); -- 教师中心
INSERT INTO sys_role_menu VALUES (102, 5001); -- 我的班级
INSERT INTO sys_role_menu VALUES (102, 5002); -- 课程管理
INSERT INTO sys_role_menu VALUES (102, 5003); -- 成绩管理
INSERT INTO sys_role_menu VALUES (102, 5004); -- 作业管理
INSERT INTO sys_role_menu VALUES (102, 5005); -- 学生管理
INSERT INTO sys_role_menu VALUES (102, 100);  -- 用户管理（部分权限）

-- 10. 开启注册功能
UPDATE sys_config SET config_value = 'true' WHERE config_key = 'sys.account.registerUser';
