{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\index.vue", "mtime": 1758178462825}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div class=\"ai-learning-page\">\r\n        <el-header class=\"header\" height=\"64px\">\r\n            <el-row justify=\"center\">\r\n                <el-col :span=\"24\">\r\n                    <div class=\"section-header\">\r\n                        <h2 class=\"section-title\">\r\n                            <i class=\"el-icon-reading\"></i>\r\n                            选择学习科目\r\n                        </h2>\r\n                        <p class=\"section-subtitle\">选择您想要学习的科目，开始您的AI学习之旅</p>\r\n                    </div>\r\n                </el-col>\r\n            </el-row>\r\n        </el-header>\r\n        <!-- 主要内容区域 -->\r\n        <el-main class=\"main-content\">\r\n            <el-container class=\"content-container\">\r\n\r\n\r\n                <!-- 学科卡片网格 -->\r\n                <el-row :gutter=\"24\" class=\"subjects-row\">\r\n                    <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\" v-for=\"(subject, index) in subjects\" :key=\"index\"\r\n                        class=\"subject-col\">\r\n                        <el-card :body-style=\"{ padding: '32px 20px' }\" class=\"subject-card\" shadow=\"hover\"\r\n                            @click.native=\"goToStudy(subject)\">\r\n                            <div class=\"subject-content\">\r\n                                <div class=\"subject-icon\">\r\n                                    <i :class=\"subject.icon\"></i>\r\n                                </div>\r\n                                <div class=\"subject-name\">{{ subject.name }}</div>\r\n                                <div class=\"subject-description\">{{ subject.description }}</div>\r\n                                <el-button type=\"text\" class=\"study-btn\">\r\n                                    开始学习\r\n                                </el-button>\r\n                            </div>\r\n                        </el-card>\r\n                    </el-col>\r\n                </el-row>\r\n            </el-container>\r\n        </el-main>\r\n\r\n        <site-footer />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport SiteFooter from '@/components/SiteFooter'\r\nexport default {\r\n    name: 'AILearningPage',\r\n    components: { SiteFooter },\r\n    data() {\r\n        return {\r\n            activeIndex: 'home',\r\n            loginLoading: false,\r\n            registerLoading: false,\r\n            subjects: [\r\n                {\r\n                    name: '小学数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '启蒙阶段，重视兴趣培养与基础能力：识字、拼音、四则运算、科学常识等。课程注重体验式学习和良好习惯形成。',\r\n                    link: '/primary',\r\n                    color: '#409EFF'\r\n                },\r\n                {\r\n                    name: '初中数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '启蒙阶段，重视兴趣培养与基础能力：识字、拼音、四则运算、科学常识等。课程注重体验式学习和良好习惯形成。',\r\n                    link: '/primary',\r\n                    color: '#409EFF'\r\n                },\r\n                {\r\n                    name: '高中数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '系统夯实学科基础：语数英全面提升，物化生引入概念与实验探究，历史地理形成时空观念与区域认知。',\r\n                    link: '/junior',\r\n                    color: '#67C23A'\r\n                },\r\n                {\r\n                    name: '小学英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '系统夯实学科基础：语数英全面提升，物化生引入概念与实验探究，历史地理形成时空观念与区域认知。',\r\n                    link: '/junior',\r\n                    color: '#67C23A'\r\n                },\r\n                {\r\n                    name: '初中英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '学科深化与思维拔高：函数与几何、物理建模、化学反应机理、生物遗传与生态、文学鉴赏与写作、英语高阶应用。',\r\n                    link: '/senior',\r\n                    color: '#E6A23C'\r\n                },\r\n                {\r\n                    name: '高中英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '学科深化与思维拔高：函数与几何、物理建模、化学反应机理、生物遗传与生态、文学鉴赏与写作、英语高阶应用。',\r\n                    link: '/senior',\r\n                    color: '#E6A23C'\r\n                },\r\n            ]\r\n        }\r\n    },\r\n    methods: {\r\n        goToStudy(subject) {\r\n            this.$message({\r\n                message: `正在进入${subject.name}学习页面...`,\r\n                type: 'success',\r\n                duration: 1500\r\n            });\r\n\r\n            // 模拟加载延迟\r\n            setTimeout(() => {\r\n                this.$router.push(subject.link);\r\n            }, 500);\r\n        },\r\n\r\n        handleLogin() {\r\n            this.loginLoading = true;\r\n            setTimeout(() => {\r\n                this.loginLoading = false;\r\n                this.$router.push('/login');\r\n            }, 1000);\r\n        },\r\n\r\n        handleRegister() {\r\n            this.registerLoading = true;\r\n            setTimeout(() => {\r\n                this.registerLoading = false;\r\n                this.$router.push('/register');\r\n            }, 1000);\r\n        },\r\n\r\n        handleSelect(key) {\r\n            this.activeIndex = key;\r\n            switch (key) {\r\n                case 'home':\r\n                    this.$message('您已在首页');\r\n                    break;\r\n                case 'about':\r\n                    this.$message('关于我们页面开发中...');\r\n                    break;\r\n                case 'help':\r\n                    this.$message('帮助页面开发中...');\r\n                    break;\r\n            }\r\n        }\r\n    },\r\n\r\n    mounted() {\r\n        // 页面加载完成后的初始化\r\n        this.$nextTick(() => {\r\n            this.$message({\r\n                message: '欢迎使用灵渡AI学习助手！',\r\n                type: 'success',\r\n                duration: 3000\r\n            });\r\n        });\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.ai-learning-page {\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n/* 头部样式 */\r\n.header {\r\n    margin-top: 20px;\r\n}\r\n\r\n.header-row {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 20px;\r\n}\r\n\r\n.logo-section {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.logo {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border-radius: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n    font-size: 20px;\r\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n    margin: 0;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.nav-col {\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.nav-menu {\r\n    border-bottom: none;\r\n    background: transparent;\r\n}\r\n\r\n.nav-menu .el-menu-item {\r\n    color: #606266;\r\n    font-weight: 500;\r\n    border-bottom: 2px solid transparent;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.nav-menu .el-menu-item:hover,\r\n.nav-menu .el-menu-item.is-active {\r\n    color: #409EFF;\r\n    border-bottom-color: #409EFF;\r\n    background: transparent;\r\n}\r\n\r\n.auth-col {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n}\r\n\r\n.auth-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n}\r\n\r\n.login-btn {\r\n    border: 1px solid #409EFF;\r\n    color: #409EFF;\r\n    background: transparent;\r\n    border-radius: 6px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.login-btn:hover {\r\n    background-color: #409EFF;\r\n    color: white;\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n    padding: 60px 0;\r\n    background: transparent;\r\n    flex: 1;\r\n}\r\n\r\n.content-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 60px 20px;\r\n}\r\n\r\n.section-header {\r\n    text-align: center;\r\n    margin-bottom: 60px;\r\n}\r\n\r\n.section-title {\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n    color: #303133;\r\n    margin: 0 0 16px 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 12px;\r\n}\r\n\r\n.section-title i {\r\n    color: #409EFF;\r\n    font-size: 32px;\r\n}\r\n\r\n.section-subtitle {\r\n    font-size: 16px;\r\n    color: #909399;\r\n    margin: 0;\r\n    font-weight: 400;\r\n}\r\n\r\n/* 学科卡片网格 */\r\n.subjects-row {\r\n    margin-top: 5px;\r\n}\r\n\r\n.subject-col {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.subject-card {\r\n    border-radius: 16px;\r\n    border: 1px solid #e4e7ed;\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    height: 250px;\r\n}\r\n\r\n/* 让卡片内容填满高度并垂直居中，保证每张卡片视觉高度一致 */\r\n.subject-card .el-card__body {\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.subject-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 4px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    transform: scaleX(0);\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.subject-card:hover::before {\r\n    transform: scaleX(1);\r\n}\r\n\r\n.subject-card:hover {\r\n    transform: translateY(-8px);\r\n    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\r\n    border-color: #409EFF;\r\n}\r\n\r\n.subject-content {\r\n    text-align: center;\r\n    position: relative;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 0px;\r\n}\r\n\r\n.subject-icon {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.subject-icon i {\r\n    font-size: 48px;\r\n    color: #409EFF;\r\n    display: block;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.subject-card:hover .subject-icon i {\r\n    transform: scale(1.1);\r\n    color: #667eea;\r\n}\r\n\r\n.subject-name {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.subject-description {\r\n    font-size: 14px;\r\n    color: #909399;\r\n    margin-bottom: 20px;\r\n    line-height: 1.5;\r\n    text-align: center;\r\n    max-width: 90%;\r\n    display: -webkit-box;\r\n    line-clamp: 2;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.study-btn {\r\n    color: #409EFF;\r\n    font-weight: 500;\r\n    padding: 8px 16px;\r\n    border-radius: 6px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.study-btn:hover {\r\n    background-color: #ecf5ff;\r\n    color: #409EFF;\r\n}\r\n\r\n/* 底部样式 */\r\n.footer {\r\n    background: rgba(255, 255, 255, 0.8);\r\n    backdrop-filter: blur(10px);\r\n    border-top: 1px solid #e4e7ed;\r\n    margin-top: auto;\r\n}\r\n\r\n.footer-content {\r\n    text-align: center;\r\n}\r\n\r\n.footer-content p {\r\n    color: #909399;\r\n    font-size: 14px;\r\n    margin: 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .header-row {\r\n        padding: 0 16px;\r\n    }\r\n\r\n    .title {\r\n        font-size: 18px;\r\n    }\r\n\r\n    .nav-col {\r\n        display: none;\r\n    }\r\n\r\n    .auth-col {\r\n        justify-content: center;\r\n    }\r\n\r\n    .auth-buttons {\r\n        gap: 8px;\r\n    }\r\n\r\n    .section-title {\r\n        font-size: 28px;\r\n        flex-direction: column;\r\n        gap: 8px;\r\n    }\r\n\r\n    .section-subtitle {\r\n        font-size: 14px;\r\n    }\r\n\r\n    .subjects-row {\r\n        margin-top: 30px;\r\n    }\r\n\r\n    .subject-card {\r\n        margin-bottom: 16px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .main-content {\r\n        padding: 40px 0;\r\n    }\r\n\r\n    .section-title {\r\n        font-size: 24px;\r\n    }\r\n\r\n    .subject-icon i {\r\n        font-size: 36px;\r\n    }\r\n\r\n    .subject-name {\r\n        font-size: 18px;\r\n    }\r\n}\r\n\r\n/* Element UI 组件样式覆盖 */\r\n.el-header {\r\n    padding: 0;\r\n}\r\n\r\n.el-main {\r\n    padding: 0;\r\n}\r\n\r\n.el-footer {\r\n    padding: 0;\r\n}\r\n\r\n.el-card {\r\n    border: none;\r\n}\r\n\r\n.el-card:hover {\r\n    border-color: #409EFF;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n.subject-card {\r\n    animation: fadeInUp 0.6s ease-out;\r\n}\r\n\r\n.subject-card:nth-child(1) {\r\n    animation-delay: 0.1s;\r\n}\r\n\r\n.subject-card:nth-child(2) {\r\n    animation-delay: 0.2s;\r\n}\r\n\r\n.subject-card:nth-child(3) {\r\n    animation-delay: 0.3s;\r\n}\r\n\r\n.subject-card:nth-child(4) {\r\n    animation-delay: 0.4s;\r\n}\r\n\r\n.subject-card:nth-child(5) {\r\n    animation-delay: 0.5s;\r\n}\r\n\r\n.subject-card:nth-child(6) {\r\n    animation-delay: 0.6s;\r\n}\r\n\r\n.subject-card:nth-child(7) {\r\n    animation-delay: 0.7s;\r\n}\r\n\r\n.subject-card:nth-child(8) {\r\n    animation-delay: 0.8s;\r\n}\r\n</style>"]}]}