package com.lingdu.common.core.domain.model;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 学生注册请求体
 * 
 * <AUTHOR>
 */
public class StudentRegisterBody extends RegisterBody
{
    /** 真实姓名 */
    @NotBlank(message = "真实姓名不能为空")
    @Size(min = 2, max = 30, message = "真实姓名长度必须在2到30个字符之间")
    private String realName;

    /** 学段 */
    @NotBlank(message = "学段不能为空")
    private String gradeLevel;

    /** 主学科 */
    @NotBlank(message = "主学科不能为空")
    private String primarySubject;

    /** 副学科，逗号分隔 */
    private String secondarySubjects;

    /** 会员类型 */
    private String memberType = "free_trial";

    /** 学校名称 */
    private String schoolName;

    /** 班级名称 */
    private String className;

    /** 身份证号 */
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;

    public String getRealName()
    {
        return realName;
    }

    public void setRealName(String realName)
    {
        this.realName = realName;
    }

    public String getGradeLevel()
    {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel)
    {
        this.gradeLevel = gradeLevel;
    }

    public String getPrimarySubject()
    {
        return primarySubject;
    }

    public void setPrimarySubject(String primarySubject)
    {
        this.primarySubject = primarySubject;
    }

    public String getSecondarySubjects()
    {
        return secondarySubjects;
    }

    public void setSecondarySubjects(String secondarySubjects)
    {
        this.secondarySubjects = secondarySubjects;
    }

    public String getMemberType()
    {
        return memberType;
    }

    public void setMemberType(String memberType)
    {
        this.memberType = memberType;
    }

    public String getSchoolName()
    {
        return schoolName;
    }

    public void setSchoolName(String schoolName)
    {
        this.schoolName = schoolName;
    }

    public String getClassName()
    {
        return className;
    }

    public void setClassName(String className)
    {
        this.className = className;
    }

    public String getIdCard()
    {
        return idCard;
    }

    public void setIdCard(String idCard)
    {
        this.idCard = idCard;
    }
}
