{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\help\\index.vue?vue&type=style&index=0&id=66580c1e&scoped=true&lang=css", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\help\\index.vue", "mtime": 1758178462824}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757926224263}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757926225750}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757926224811}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKDQouaGVscC1wYWdlIHsNCiAgICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQouaGVybyB7DQogICAgbWFyZ2luOiAwIGF1dG87DQogICAgbWF4LXdpZHRoOiAxMTAwcHg7DQogICAgYm9yZGVyOiBub25lOw0KICAgIGJhY2tncm91bmQ6ICNmNWY3ZmE7DQp9DQoNCi50aXRsZSB7DQogICAgbWFyZ2luOiAwOw0KICAgIHBhZGRpbmc6IDE2cHggOHB4IDRweDsNCiAgICBmb250LXNpemU6IDIycHg7DQogICAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5zdWJ0aXRsZSB7DQogICAgbWFyZ2luOiAwIDAgMTZweDsNCiAgICBjb2xvcjogIzkwOTM5OTsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/help", "sourcesContent": ["<template>\r\n    <div class=\"help-page\">\r\n        <el-card class=\"hero\" shadow=\"never\">\r\n            <h1 class=\"title\">帮助中心</h1>\r\n            <p class=\"subtitle\">常见问题与使用指南正在完善中。</p>\r\n        </el-card>\r\n        <site-footer />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport SiteFooter from '@/components/SiteFooter'\r\nexport default {\r\n    name: 'HelpPage',\r\n    components: { SiteFooter }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.help-page {\r\n    padding: 20px;\r\n}\r\n\r\n.hero {\r\n    margin: 0 auto;\r\n    max-width: 1100px;\r\n    border: none;\r\n    background: #f5f7fa;\r\n}\r\n\r\n.title {\r\n    margin: 0;\r\n    padding: 16px 8px 4px;\r\n    font-size: 22px;\r\n    font-weight: 700;\r\n    text-align: center;\r\n}\r\n\r\n.subtitle {\r\n    margin: 0 0 16px;\r\n    color: #909399;\r\n    text-align: center;\r\n}\r\n</style>\r\n"]}]}