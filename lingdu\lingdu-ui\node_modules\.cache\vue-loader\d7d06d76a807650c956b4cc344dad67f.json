{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\layout\\index.vue", "mtime": 1758178462817}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBBcHBNYWluLCBOYXZiYXIsIFNldHRpbmdzLCBTaWRlYmFyLCBUYWdzVmlldyB9IGZyb20gJy4vY29tcG9uZW50cycNCmltcG9ydCBSZXNpemVNaXhpbiBmcm9tICcuL21peGluL1Jlc2l6ZUhhbmRsZXInDQppbXBvcnQgeyBtYXBTdGF0ZSB9IGZyb20gJ3Z1ZXgnDQppbXBvcnQgdmFyaWFibGVzIGZyb20gJ0AvYXNzZXRzL3N0eWxlcy92YXJpYWJsZXMuc2NzcycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnTGF5b3V0JywNCiAgY29tcG9uZW50czogew0KICAgIEFwcE1haW4sDQogICAgTmF2YmFyLA0KICAgIFNldHRpbmdzLA0KICAgIFNpZGViYXIsDQogICAgVGFnc1ZpZXcNCiAgfSwNCiAgbWl4aW5zOiBbUmVzaXplTWl4aW5dLA0KICBjb21wdXRlZDogew0KICAgIC4uLm1hcFN0YXRlKHsNCiAgICAgIHRoZW1lOiBzdGF0ZSA9PiBzdGF0ZS5zZXR0aW5ncy50aGVtZSwNCiAgICAgIHNpZGVUaGVtZTogc3RhdGUgPT4gc3RhdGUuc2V0dGluZ3Muc2lkZVRoZW1lLA0KICAgICAgc2lkZWJhcjogc3RhdGUgPT4gc3RhdGUuYXBwLnNpZGViYXIsDQogICAgICBkZXZpY2U6IHN0YXRlID0+IHN0YXRlLmFwcC5kZXZpY2UsDQogICAgICBuZWVkVGFnc1ZpZXc6IHN0YXRlID0+IHN0YXRlLnNldHRpbmdzLnRhZ3NWaWV3LA0KICAgICAgZml4ZWRIZWFkZXI6IHN0YXRlID0+IHN0YXRlLnNldHRpbmdzLmZpeGVkSGVhZGVyDQogICAgfSksDQogICAgY2xhc3NPYmooKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBoaWRlU2lkZWJhcjogIXRoaXMuc2lkZWJhci5vcGVuZWQsDQogICAgICAgIG9wZW5TaWRlYmFyOiB0aGlzLnNpZGViYXIub3BlbmVkLA0KICAgICAgICB3aXRob3V0QW5pbWF0aW9uOiB0aGlzLnNpZGViYXIud2l0aG91dEFuaW1hdGlvbiwNCiAgICAgICAgbW9iaWxlOiB0aGlzLmRldmljZSA9PT0gJ21vYmlsZScNCiAgICAgIH0NCiAgICB9LA0KICAgIHZhcmlhYmxlcygpIHsNCiAgICAgIHJldHVybiB2YXJpYWJsZXMNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVDbGlja091dHNpZGUoKSB7DQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXBwL2Nsb3NlU2lkZUJhcicsIHsgd2l0aG91dEFuaW1hdGlvbjogZmFsc2UgfSkNCiAgICB9LA0KICAgIHNldExheW91dCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuc2V0dGluZ1JlZi5vcGVuU2V0dGluZygpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\r\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{ '--current-color': theme }\">\r\n    <div v-if=\"device === 'mobile' && sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\r\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\" />\r\n    <div :class=\"{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }\" class=\"main-container\">\r\n      <div :class=\"{ 'fixed-header': fixedHeader }\">\r\n        <navbar @setLayout=\"setLayout\" />\r\n        <tags-view v-if=\"needTagsView\" />\r\n      </div>\r\n      <app-main />\r\n      <settings ref=\"settingRef\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\r\nimport ResizeMixin from './mixin/ResizeHandler'\r\nimport { mapState } from 'vuex'\r\nimport variables from '@/assets/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    Settings,\r\n    Sidebar,\r\n    TagsView\r\n  },\r\n  mixins: [ResizeMixin],\r\n  computed: {\r\n    ...mapState({\r\n      theme: state => state.settings.theme,\r\n      sideTheme: state => state.settings.sideTheme,\r\n      sidebar: state => state.app.sidebar,\r\n      device: state => state.app.device,\r\n      needTagsView: state => state.settings.tagsView,\r\n      fixedHeader: state => state.settings.fixedHeader\r\n    }),\r\n    classObj() {\r\n      return {\r\n        hideSidebar: !this.sidebar.opened,\r\n        openSidebar: this.sidebar.opened,\r\n        withoutAnimation: this.sidebar.withoutAnimation,\r\n        mobile: this.device === 'mobile'\r\n      }\r\n    },\r\n    variables() {\r\n      return variables\r\n    }\r\n  },\r\n  methods: {\r\n    handleClickOutside() {\r\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n    },\r\n    setLayout() {\r\n      this.$refs.settingRef.openSetting()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n@import \"~@/assets/styles/variables.scss\";\r\n\r\n.app-wrapper {\r\n  @include clearfix;\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n\r\n  &.mobile.openSidebar {\r\n    position: fixed;\r\n    top: 0;\r\n  }\r\n}\r\n\r\n.main-container:has(.fixed-header) {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.drawer-bg {\r\n  background: #000;\r\n  opacity: 0.3;\r\n  width: 100%;\r\n  top: 0;\r\n  height: 100%;\r\n  position: absolute;\r\n  z-index: 999;\r\n}\r\n\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  z-index: 9;\r\n  width: calc(100% - #{$base-sidebar-width});\r\n  transition: width 0.28s;\r\n}\r\n\r\n.hideSidebar .fixed-header {\r\n  width: calc(100% - 54px);\r\n}\r\n\r\n.sidebarHide .fixed-header {\r\n  width: 100%;\r\n}\r\n\r\n.mobile .fixed-header {\r\n  width: 100%;\r\n}\r\n</style>\r\n"]}]}