<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lingdu.system.mapper.parentInfoMapper">
    
    <resultMap type="parentInfo" id="parentInfoResult">
        <result property="parentId"    column="parent_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="phone"    column="phone"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectparentInfoVo">
        select pi.parent_id, pi.user_name, pi.nick_name, pi.phone, pi.create_time, pi.update_time 
        from parent_info pi
    </sql>

    <select id="selectparentInfoList" parameterType="parentInfo" resultMap="parentInfoResult">
        <include refid="selectparentInfoVo"/>
        <where>  
            <if test="userName != null and userName != ''"> and pi.user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null and nickName != ''"> and pi.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''"> and pi.phone = #{phone}</if>
        </where>
    </select>
    
    <select id="selectparentInfoByParentId" parameterType="Long" resultMap="parentInfoResult">
        <include refid="selectparentInfoVo"/>
        where parent_id = #{parentId}
    </select>

    <insert id="insertparentInfo" parameterType="parentInfo" useGeneratedKeys="true" keyProperty="parentId">
        insert into parent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateparentInfo" parameterType="parentInfo">
        update parent_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where parent_id = #{parentId}
    </update>

    <delete id="deleteparentInfoByParentId" parameterType="Long">
        delete from parent_info where parent_id = #{parentId}
    </delete>

    <delete id="deleteparentInfoByParentIds" parameterType="String">
        delete from parent_info where parent_id in 
        <foreach item="parentId" collection="array" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </delete>
</mapper>