import request from '@/utils/request'

// 学生注册
export function registerStudent(data) {
  return request({
    url: '/edu/register/student',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 教师注册
export function registerTeacher(data) {
  return request({
    url: '/edu/register/teacher',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 家长注册
export function registerParent(data) {
  return request({
    url: '/edu/register/parent',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 发送短信验证码
export function sendSmsCode(phoneNumber, verifyType) {
  return request({
    url: '/edu/sms/send',
    headers: {
      isToken: false
    },
    method: 'post',
    params: {
      phoneNumber,
      verifyType
    }
  })
}

// 验证短信验证码
export function verifySmsCode(phoneNumber, verifyCode, verifyType) {
  return request({
    url: '/edu/sms/verify',
    headers: {
      isToken: false
    },
    method: 'post',
    params: {
      phoneNumber,
      verifyCode,
      verifyType
    }
  })
}
