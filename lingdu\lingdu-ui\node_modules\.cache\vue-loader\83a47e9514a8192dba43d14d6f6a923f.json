{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\layout\\index.vue?vue&type=style&index=0&id=13877386&lang=scss&scoped=true", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\layout\\index.vue", "mtime": 1758178462817}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757926224263}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757926225750}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757926224811}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757926223774}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICJ+QC9hc3NldHMvc3R5bGVzL21peGluLnNjc3MiOw0KQGltcG9ydCAifkAvYXNzZXRzL3N0eWxlcy92YXJpYWJsZXMuc2NzcyI7DQoNCi5hcHAtd3JhcHBlciB7DQogIEBpbmNsdWRlIGNsZWFyZml4Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGhlaWdodDogMTAwJTsNCiAgd2lkdGg6IDEwMCU7DQoNCiAgJi5tb2JpbGUub3BlblNpZGViYXIgew0KICAgIHBvc2l0aW9uOiBmaXhlZDsNCiAgICB0b3A6IDA7DQogIH0NCn0NCg0KLm1haW4tY29udGFpbmVyOmhhcyguZml4ZWQtaGVhZGVyKSB7DQogIGhlaWdodDogMTAwdmg7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5kcmF3ZXItYmcgew0KICBiYWNrZ3JvdW5kOiAjMDAwOw0KICBvcGFjaXR5OiAwLjM7DQogIHdpZHRoOiAxMDAlOw0KICB0b3A6IDA7DQogIGhlaWdodDogMTAwJTsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB6LWluZGV4OiA5OTk7DQp9DQoNCi5maXhlZC1oZWFkZXIgew0KICBwb3NpdGlvbjogZml4ZWQ7DQogIHRvcDogMDsNCiAgcmlnaHQ6IDA7DQogIHotaW5kZXg6IDk7DQogIHdpZHRoOiBjYWxjKDEwMCUgLSAjeyRiYXNlLXNpZGViYXItd2lkdGh9KTsNCiAgdHJhbnNpdGlvbjogd2lkdGggMC4yOHM7DQp9DQoNCi5oaWRlU2lkZWJhciAuZml4ZWQtaGVhZGVyIHsNCiAgd2lkdGg6IGNhbGMoMTAwJSAtIDU0cHgpOw0KfQ0KDQouc2lkZWJhckhpZGUgLmZpeGVkLWhlYWRlciB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQoubW9iaWxlIC5maXhlZC1oZWFkZXIgew0KICB3aWR0aDogMTAwJTsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\r\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{ '--current-color': theme }\">\r\n    <div v-if=\"device === 'mobile' && sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\r\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\" />\r\n    <div :class=\"{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }\" class=\"main-container\">\r\n      <div :class=\"{ 'fixed-header': fixedHeader }\">\r\n        <navbar @setLayout=\"setLayout\" />\r\n        <tags-view v-if=\"needTagsView\" />\r\n      </div>\r\n      <app-main />\r\n      <settings ref=\"settingRef\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\r\nimport ResizeMixin from './mixin/ResizeHandler'\r\nimport { mapState } from 'vuex'\r\nimport variables from '@/assets/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    Settings,\r\n    Sidebar,\r\n    TagsView\r\n  },\r\n  mixins: [ResizeMixin],\r\n  computed: {\r\n    ...mapState({\r\n      theme: state => state.settings.theme,\r\n      sideTheme: state => state.settings.sideTheme,\r\n      sidebar: state => state.app.sidebar,\r\n      device: state => state.app.device,\r\n      needTagsView: state => state.settings.tagsView,\r\n      fixedHeader: state => state.settings.fixedHeader\r\n    }),\r\n    classObj() {\r\n      return {\r\n        hideSidebar: !this.sidebar.opened,\r\n        openSidebar: this.sidebar.opened,\r\n        withoutAnimation: this.sidebar.withoutAnimation,\r\n        mobile: this.device === 'mobile'\r\n      }\r\n    },\r\n    variables() {\r\n      return variables\r\n    }\r\n  },\r\n  methods: {\r\n    handleClickOutside() {\r\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n    },\r\n    setLayout() {\r\n      this.$refs.settingRef.openSetting()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n@import \"~@/assets/styles/variables.scss\";\r\n\r\n.app-wrapper {\r\n  @include clearfix;\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n\r\n  &.mobile.openSidebar {\r\n    position: fixed;\r\n    top: 0;\r\n  }\r\n}\r\n\r\n.main-container:has(.fixed-header) {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.drawer-bg {\r\n  background: #000;\r\n  opacity: 0.3;\r\n  width: 100%;\r\n  top: 0;\r\n  height: 100%;\r\n  position: absolute;\r\n  z-index: 999;\r\n}\r\n\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  z-index: 9;\r\n  width: calc(100% - #{$base-sidebar-width});\r\n  transition: width 0.28s;\r\n}\r\n\r\n.hideSidebar .fixed-header {\r\n  width: calc(100% - 54px);\r\n}\r\n\r\n.sidebarHide .fixed-header {\r\n  width: 100%;\r\n}\r\n\r\n.mobile .fixed-header {\r\n  width: 100%;\r\n}\r\n</style>\r\n"]}]}