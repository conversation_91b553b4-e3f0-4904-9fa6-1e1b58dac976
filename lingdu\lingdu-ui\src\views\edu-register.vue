<template>
  <div class="edu-register">
    <div class="register-container">
      <!-- 角色选择标签页 -->
      <el-tabs v-model="activeRole" class="role-tabs" @tab-click="handleRoleChange">
        <el-tab-pane label="学生注册" name="student">
          <div class="role-icon">
            <i class="el-icon-user-solid"></i>
          </div>
        </el-tab-pane>
        <el-tab-pane label="家长注册" name="parent">
          <div class="role-icon">
            <i class="el-icon-user"></i>
          </div>
        </el-tab-pane>
        <el-tab-pane label="教师注册" name="teacher">
          <div class="role-icon">
            <i class="el-icon-s-custom"></i>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 注册表单 -->
      <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form">
        <h3 class="title">{{ getRoleTitle() }}</h3>

        <!-- 基础信息 -->
        <div class="form-section">
          <h4 class="section-title">基础信息</h4>
          
          <!-- 手机号 -->
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="手机号（用于登录）"
              maxlength="11"
            >
              <svg-icon slot="prefix" icon-class="phone" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="设置登录密码"
              show-password
            >
              <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <!-- 确认密码 -->
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="确认登录密码"
              show-password
            >
              <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <!-- 真实姓名 -->
          <el-form-item prop="realName">
            <el-input
              v-model="registerForm.realName"
              placeholder="真实姓名"
            >
              <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>
        </div>

        <!-- 学生专用信息 -->
        <div class="form-section" v-if="activeRole === 'student'">
          <h4 class="section-title">学习信息</h4>
          
          <!-- 学段选择 -->
          <el-form-item prop="gradeLevel">
            <el-select v-model="registerForm.gradeLevel" placeholder="选择学段" style="width: 100%">
              <el-option-group label="小学">
                <el-option label="小学1年级" value="primary_1"></el-option>
                <el-option label="小学2年级" value="primary_2"></el-option>
                <el-option label="小学3年级" value="primary_3"></el-option>
                <el-option label="小学4年级" value="primary_4"></el-option>
                <el-option label="小学5年级" value="primary_5"></el-option>
                <el-option label="小学6年级" value="primary_6"></el-option>
              </el-option-group>
              <el-option-group label="初中">
                <el-option label="初中1年级" value="junior_1"></el-option>
                <el-option label="初中2年级" value="junior_2"></el-option>
                <el-option label="初中3年级" value="junior_3"></el-option>
              </el-option-group>
              <el-option-group label="高中">
                <el-option label="高中1年级" value="senior_1"></el-option>
                <el-option label="高中2年级" value="senior_2"></el-option>
                <el-option label="高中3年级" value="senior_3"></el-option>
              </el-option-group>
            </el-select>
          </el-form-item>

          <!-- 主学科选择 -->
          <el-form-item prop="primarySubject">
            <el-select v-model="registerForm.primarySubject" placeholder="选择主学科（必选1个）" style="width: 100%">
              <el-option label="语文" value="chinese"></el-option>
              <el-option label="数学" value="math"></el-option>
              <el-option label="英语" value="english"></el-option>
              <el-option label="物理" value="physics"></el-option>
              <el-option label="化学" value="chemistry"></el-option>
              <el-option label="生物" value="biology"></el-option>
              <el-option label="历史" value="history"></el-option>
              <el-option label="地理" value="geography"></el-option>
              <el-option label="政治" value="politics"></el-option>
            </el-select>
          </el-form-item>

          <!-- 副学科选择 -->
          <el-form-item>
            <el-select v-model="registerForm.secondarySubjects" placeholder="选择副学科（可选多个）" multiple style="width: 100%">
              <el-option label="语文" value="chinese"></el-option>
              <el-option label="数学" value="math"></el-option>
              <el-option label="英语" value="english"></el-option>
              <el-option label="物理" value="physics"></el-option>
              <el-option label="化学" value="chemistry"></el-option>
              <el-option label="生物" value="biology"></el-option>
              <el-option label="历史" value="history"></el-option>
              <el-option label="地理" value="geography"></el-option>
              <el-option label="政治" value="politics"></el-option>
            </el-select>
          </el-form-item>

          <!-- 会员类型选择 -->
          <el-form-item>
            <el-radio-group v-model="registerForm.memberType">
              <el-radio label="free_trial">
                <span class="member-option">
                  <strong>免费体验</strong>
                  <small>（7天免费，限1个学科）</small>
                </span>
              </el-radio>
              <el-radio label="basic">
                <span class="member-option">
                  <strong>基础会员</strong>
                  <small>（支持多学科学习）</small>
                </span>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 学校信息（可选） -->
          <el-form-item>
            <el-input v-model="registerForm.schoolName" placeholder="学校名称（可选）">
              <svg-icon slot="prefix" icon-class="education" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>
        </div>

        <!-- 教师专用信息 -->
        <div class="form-section" v-if="activeRole === 'teacher'">
          <h4 class="section-title">教学信息</h4>
          
          <!-- 身份证号 -->
          <el-form-item prop="idCard">
            <el-input
              v-model="registerForm.idCard"
              placeholder="身份证号"
              maxlength="18"
            >
              <svg-icon slot="prefix" icon-class="idcard" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <!-- 教师资格证编号 -->
          <el-form-item prop="teacherCertNumber">
            <el-input
              v-model="registerForm.teacherCertNumber"
              placeholder="教师资格证编号"
            >
              <svg-icon slot="prefix" icon-class="documentation" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <!-- 教学学段 -->
          <el-form-item prop="teachingGradeLevels">
            <el-select v-model="registerForm.teachingGradeLevels" placeholder="选择教学学段（可多选）" multiple style="width: 100%">
              <el-option label="小学" value="primary"></el-option>
              <el-option label="初中" value="junior"></el-option>
              <el-option label="高中" value="senior"></el-option>
            </el-select>
          </el-form-item>

          <!-- 教学学科 -->
          <el-form-item prop="teachingSubjects">
            <el-select v-model="registerForm.teachingSubjects" placeholder="选择教学学科（限1-2个）" multiple style="width: 100%">
              <el-option label="语文" value="chinese"></el-option>
              <el-option label="数学" value="math"></el-option>
              <el-option label="英语" value="english"></el-option>
              <el-option label="物理" value="physics"></el-option>
              <el-option label="化学" value="chemistry"></el-option>
              <el-option label="生物" value="biology"></el-option>
              <el-option label="历史" value="history"></el-option>
              <el-option label="地理" value="geography"></el-option>
              <el-option label="政治" value="politics"></el-option>
            </el-select>
          </el-form-item>

          <!-- 任职学校 -->
          <el-form-item>
            <el-input v-model="registerForm.schoolName" placeholder="任职学校（可选）">
              <svg-icon slot="prefix" icon-class="education" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <!-- 教师资格证上传 -->
          <el-form-item>
            <el-upload
              class="cert-upload"
              drag
              action="#"
              :auto-upload="false"
              :on-change="handleCertUpload"
              accept="image/*"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将教师资格证照片拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2MB</div>
            </el-upload>
          </el-form-item>
        </div>

        <!-- 家长专用信息 -->
        <div class="form-section" v-if="activeRole === 'parent'">
          <h4 class="section-title">权限设置</h4>
          
          <!-- 权限级别选择 -->
          <el-form-item>
            <el-radio-group v-model="registerForm.permissionLevel">
              <el-radio label="view_only">仅查看学习进度</el-radio>
              <el-radio label="view_notify">查看进度 + 接收通知</el-radio>
              <el-radio label="view_communicate">查看进度 + 沟通老师</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- 验证码 -->
        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input
            v-model="registerForm.code"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
          >
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </el-input>
          <div class="register-code">
            <img :src="codeUrl" @click="getCode" class="register-code-img"/>
          </div>
        </el-form-item>

        <!-- 注册按钮 -->
        <el-form-item style="width:100%;">
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            style="width:100%;"
            @click.native.prevent="handleRegister"
          >
            <span v-if="!loading">{{ getRegisterButtonText() }}</span>
            <span v-else>注 册 中...</span>
          </el-button>
        </el-form-item>

        <!-- 登录链接 -->
        <div class="login-link">
          <router-link class="link-type" to="/edu-login">已有账户？立即登录</router-link>
        </div>
      </el-form>
    </div>

    <!-- 底部版权 -->
    <div class="el-register-footer">
      <span>Copyright © 2018-2025 lingdu.edu All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login"
import { registerStudent, registerTeacher, registerParent } from "@/api/edu"

export default {
  name: "EduRegister",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error("两次输入的密码不一致"))
      } else {
        callback()
      }
    }

    const validatePhoneNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号'))
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }

    const validateIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入身份证号'))
      } else if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
        callback(new Error('请输入正确的身份证号'))
      } else {
        callback()
      }
    }

    return {
      activeRole: 'student',
      codeUrl: "",
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        realName: "",
        code: "",
        uuid: "",
        // 学生专用字段
        gradeLevel: "",
        primarySubject: "",
        secondarySubjects: [],
        memberType: "free_trial",
        schoolName: "",
        className: "",
        idCard: "",
        // 教师专用字段
        teacherCertNumber: "",
        teachingGradeLevels: [],
        teachingSubjects: [],
        teacherCertImage: "",
        // 家长专用字段
        permissionLevel: "view_only"
      },
      registerRules: {
        username: [
          { required: true, trigger: "blur", validator: validatePhoneNumber }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入密码" },
          { min: 5, max: 20, message: "密码长度必须介于 5 和 20 之间", trigger: "blur" },
          { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\\ |", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请再次输入密码" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ],
        realName: [
          { required: true, trigger: "blur", message: "请输入真实姓名" },
          { min: 2, max: 30, message: "姓名长度必须介于 2 和 30 之间", trigger: "blur" }
        ],
        gradeLevel: [
          { required: true, trigger: "change", message: "请选择学段" }
        ],
        primarySubject: [
          { required: true, trigger: "change", message: "请选择主学科" }
        ],
        idCard: [
          { validator: validateIdCard, trigger: "blur" }
        ],
        teacherCertNumber: [
          { required: true, trigger: "blur", message: "请输入教师资格证编号" }
        ],
        teachingGradeLevels: [
          { required: true, trigger: "change", message: "请选择教学学段" }
        ],
        teachingSubjects: [
          { required: true, trigger: "change", message: "请选择教学学科" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      captchaEnabled: true
    }
  },
  created() {
    this.getCode()
    // 从路由参数获取角色
    if (this.$route.query.role) {
      this.activeRole = this.$route.query.role
    }
  },
  methods: {
    // 角色切换
    handleRoleChange(tab) {
      this.activeRole = tab.name
      this.resetForm()
    },

    // 获取角色标题
    getRoleTitle() {
      const titles = {
        student: '学生注册',
        parent: '家长注册',
        teacher: '教师注册'
      }
      return titles[this.activeRole] || '用户注册'
    },

    // 获取注册按钮文本
    getRegisterButtonText() {
      const texts = {
        student: '注册学生账号',
        parent: '注册家长账号',
        teacher: '提交教师认证'
      }
      return texts[this.activeRole] || '立即注册'
    },

    // 获取验证码
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img
          this.registerForm.uuid = res.uuid
        }
      })
    },

    // 处理教师资格证上传
    handleCertUpload(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        this.registerForm.teacherCertImage = e.target.result
      }
      reader.readAsDataURL(file.raw)
    },

    // 重置表单
    resetForm() {
      this.$refs.registerForm.resetFields()
      this.registerForm = {
        username: "",
        password: "",
        confirmPassword: "",
        realName: "",
        code: "",
        uuid: this.registerForm.uuid,
        gradeLevel: "",
        primarySubject: "",
        secondarySubjects: [],
        memberType: "free_trial",
        schoolName: "",
        className: "",
        idCard: "",
        teacherCertNumber: "",
        teachingGradeLevels: [],
        teachingSubjects: [],
        teacherCertImage: "",
        permissionLevel: "view_only"
      }
    },

    // 处理注册
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          // 教师学科数量限制
          if (this.activeRole === 'teacher' && this.registerForm.teachingSubjects.length > 2) {
            this.$message.error('教学学科最多选择2个')
            return
          }

          this.loading = true

          // 根据角色调用不同的注册接口
          let registerPromise
          const formData = { ...this.registerForm }

          // 处理数组字段
          if (formData.secondarySubjects && Array.isArray(formData.secondarySubjects)) {
            formData.secondarySubjects = formData.secondarySubjects.join(',')
          }
          if (formData.teachingGradeLevels && Array.isArray(formData.teachingGradeLevels)) {
            formData.teachingGradeLevels = formData.teachingGradeLevels.join(',')
          }
          if (formData.teachingSubjects && Array.isArray(formData.teachingSubjects)) {
            formData.teachingSubjects = formData.teachingSubjects.join(',')
          }

          switch (this.activeRole) {
            case 'student':
              registerPromise = registerStudent(formData)
              break
            case 'teacher':
              registerPromise = registerTeacher(formData)
              break
            case 'parent':
              registerPromise = registerParent(formData)
              break
            default:
              this.$message.error('无效的注册类型')
              this.loading = false
              return
          }

          registerPromise.then(res => {
            this.loading = false
            const username = this.registerForm.username
            let message = ''

            switch (this.activeRole) {
              case 'student':
                message = `恭喜你，学生账号 ${username} 注册成功！`
                break
              case 'teacher':
                message = `恭喜你，教师账号 ${username} 注册成功！您的资格正在审核中，审核通过后即可使用。`
                break
              case 'parent':
                message = `恭喜你，家长账号 ${username} 注册成功！请登录后关联您的孩子。`
                break
            }

            this.$alert(message, '注册成功', {
              dangerouslyUseHTMLString: true,
              type: 'success'
            }).then(() => {
              this.$router.push("/edu-login")
            }).catch(() => {})
          }).catch(() => {
            this.loading = false
            if (this.captchaEnabled) {
              this.getCode()
            }
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.edu-register {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0;
}

.register-container {
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 500px;
  max-width: 90vw;
  padding: 0;
  overflow: hidden;
}

.role-tabs {
  .el-tabs__header {
    margin: 0;
    background: #f8f9fa;
  }

  .el-tabs__nav-wrap {
    padding: 0;
  }

  .el-tabs__item {
    height: 60px;
    line-height: 60px;
    font-size: 16px;
    font-weight: 500;

    &.is-active {
      color: #409EFF;
      background: #ffffff;
    }
  }

  .role-icon {
    text-align: center;
    padding: 10px 0;

    i {
      font-size: 24px;
      color: #909399;
    }
  }

  .el-tabs__item.is-active .role-icon i {
    color: #409EFF;
  }
}

.register-form {
  padding: 30px 40px 40px;
  max-height: 70vh;
  overflow-y: auto;

  .title {
    margin: 0 0 30px 0;
    text-align: center;
    color: #303133;
    font-weight: bold;
    font-size: 22px;
  }

  .form-section {
    margin-bottom: 25px;

    .section-title {
      color: #409EFF;
      font-size: 16px;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #EBEEF5;
    }
  }

  .el-input {
    height: 40px;
    input {
      height: 40px;
      border-radius: 6px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }

  .member-option {
    display: block;

    small {
      display: block;
      color: #909399;
      font-size: 12px;
      margin-top: 2px;
    }
  }

  .cert-upload {
    .el-upload {
      width: 100%;
    }

    .el-upload-dragger {
      width: 100%;
      height: 120px;
    }
  }

  .register-code {
    width: 35%;
    height: 40px;
    float: right;
    img {
      cursor: pointer;
      vertical-align: middle;
      height: 40px;
      border-radius: 6px;
    }
  }

  .login-link {
    text-align: center;
    margin-top: 20px;

    .link-type {
      color: #409EFF;
      text-decoration: none;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
