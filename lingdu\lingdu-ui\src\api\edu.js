import request from '@/utils/request'

// 学生注册
export function registerStudent(data) {
  return request({
    url: '/edu/register/student',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 教师注册
export function registerTeacher(data) {
  return request({
    url: '/edu/register/teacher',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 家长注册
export function registerParent(data) {
  return request({
    url: '/edu/register/parent',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 发送短信验证码
export function sendSmsCode(phoneNumber, verifyType) {
  return request({
    url: '/edu/sms/send',
    headers: {
      isToken: false
    },
    method: 'post',
    params: {
      phoneNumber,
      verifyType
    }
  })
}

// 验证短信验证码
export function verifySmsCode(phoneNumber, verifyCode, verifyType) {
  return request({
    url: '/edu/sms/verify',
    headers: {
      isToken: false
    },
    method: 'post',
    params: {
      phoneNumber,
      verifyCode,
      verifyType
    }
  })
}

// 家长关联学生
export function bindStudent(data) {
  return request({
    url: '/edu/parent/bind-student',
    method: 'post',
    data: data
  })
}

// 获取学段列表
export function getGradeLevels() {
  return request({
    url: '/edu/dict/grade-levels',
    method: 'get'
  })
}

// 获取学科列表
export function getSubjects() {
  return request({
    url: '/edu/dict/subjects',
    method: 'get'
  })
}

// 获取会员类型列表
export function getMemberTypes() {
  return request({
    url: '/edu/dict/member-types',
    method: 'get'
  })
}

// 教育系统登录方法
export function eduLogin(data) {
  return request({
    url: '/edu/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}

// 角色切换
export function switchRole(roleType, contextData) {
  return request({
    url: '/edu/switch-role',
    method: 'post',
    data: {
      roleType,
      contextData
    }
  })
}

// 获取用户角色列表
export function getUserRoles() {
  return request({
    url: '/edu/user-roles',
    method: 'get'
  })
}

// 获取学生信息
export function getStudentInfo() {
  return request({
    url: '/edu/student/info',
    method: 'get'
  })
}

// 获取教师信息
export function getTeacherInfo() {
  return request({
    url: '/edu/teacher/info',
    method: 'get'
  })
}

// 获取家长关联的学生列表
export function getParentStudents() {
  return request({
    url: '/edu/parent/students',
    method: 'get'
  })
}
