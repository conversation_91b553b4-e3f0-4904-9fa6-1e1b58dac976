package com.lingdu.common.core.domain.model;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 家长关联学生请求体
 * 
 * <AUTHOR>
 */
public class ParentBindStudentBody
{
    /** 学生手机号或用户名 */
    @NotBlank(message = "学生手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String studentPhone;

    /** 学生姓名 */
    @NotBlank(message = "学生姓名不能为空")
    private String studentName;

    /** 学号（可选） */
    private String studentNumber;

    /** 验证码 */
    @NotBlank(message = "验证码不能为空")
    private String verifyCode;

    /** 权限级别 */
    private String permissionLevel = "view_only";

    /** 关系类型 */
    private String relationType = "parent";

    public String getStudentPhone()
    {
        return studentPhone;
    }

    public void setStudentPhone(String studentPhone)
    {
        this.studentPhone = studentPhone;
    }

    public String getStudentName()
    {
        return studentName;
    }

    public void setStudentName(String studentName)
    {
        this.studentName = studentName;
    }

    public String getStudentNumber()
    {
        return studentNumber;
    }

    public void setStudentNumber(String studentNumber)
    {
        this.studentNumber = studentNumber;
    }

    public String getVerifyCode()
    {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode)
    {
        this.verifyCode = verifyCode;
    }

    public String getPermissionLevel()
    {
        return permissionLevel;
    }

    public void setPermissionLevel(String permissionLevel)
    {
        this.permissionLevel = permissionLevel;
    }

    public String getRelationType()
    {
        return relationType;
    }

    public void setRelationType(String relationType)
    {
        this.relationType = relationType;
    }
}
