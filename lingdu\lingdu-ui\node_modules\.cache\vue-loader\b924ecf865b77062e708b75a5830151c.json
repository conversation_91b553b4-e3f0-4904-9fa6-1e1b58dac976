{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-login.vue?vue&type=template&id=625b2e30", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-login.vue", "mtime": 1758185473790}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1757926225790}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}