{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-register.vue", "mtime": 1758185596148}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1757925919067}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "_edu", "name", "data", "_this", "equalToPassword", "rule", "value", "callback", "registerForm", "password", "Error", "validatePhoneNumber", "test", "validateIdCard", "activeRole", "codeUrl", "username", "confirmPassword", "realName", "code", "uuid", "gradeLevel", "primarySubject", "secondarySubjects", "memberType", "schoolName", "className", "idCard", "teacher<PERSON><PERSON><PERSON><PERSON><PERSON>", "teachingGradeLevels", "teachingSubjects", "teacherCertImage", "permissionLevel", "registerRules", "required", "trigger", "validator", "message", "min", "max", "pattern", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created", "getCode", "$route", "query", "role", "methods", "handleRoleChange", "tab", "resetForm", "getRoleTitle", "titles", "student", "parent", "teacher", "getRegisterButtonText", "texts", "_this2", "getCodeImg", "then", "res", "undefined", "img", "handleCertUpload", "file", "_this3", "reader", "FileReader", "onload", "e", "target", "result", "readAsDataURL", "raw", "$refs", "resetFields", "handleRegister", "_this4", "validate", "valid", "length", "$message", "error", "registerPromise", "formData", "_objectSpread2", "default", "Array", "isArray", "join", "registerStudent", "registerTeacher", "registerParent", "concat", "$alert", "dangerouslyUseHTMLString", "type", "$router", "push", "catch"], "sources": ["src/views/edu-register.vue"], "sourcesContent": ["<template>\n  <div class=\"edu-register\">\n    <div class=\"register-container\">\n      <!-- 角色选择标签页 -->\n      <el-tabs v-model=\"activeRole\" class=\"role-tabs\" @tab-click=\"handleRoleChange\">\n        <el-tab-pane label=\"学生注册\" name=\"student\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-user-solid\"></i>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"家长注册\" name=\"parent\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-user\"></i>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"教师注册\" name=\"teacher\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-s-custom\"></i>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <!-- 注册表单 -->\n      <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n        <h3 class=\"title\">{{ getRoleTitle() }}</h3>\n\n        <!-- 基础信息 -->\n        <div class=\"form-section\">\n          <h4 class=\"section-title\">基础信息</h4>\n          \n          <!-- 手机号 -->\n          <el-form-item prop=\"username\">\n            <el-input\n              v-model=\"registerForm.username\"\n              placeholder=\"手机号（用于登录）\"\n              maxlength=\"11\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"phone\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 密码 -->\n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"registerForm.password\"\n              type=\"password\"\n              placeholder=\"设置登录密码\"\n              show-password\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 确认密码 -->\n          <el-form-item prop=\"confirmPassword\">\n            <el-input\n              v-model=\"registerForm.confirmPassword\"\n              type=\"password\"\n              placeholder=\"确认登录密码\"\n              show-password\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 真实姓名 -->\n          <el-form-item prop=\"realName\">\n            <el-input\n              v-model=\"registerForm.realName\"\n              placeholder=\"真实姓名\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n        </div>\n\n        <!-- 学生专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'student'\">\n          <h4 class=\"section-title\">学习信息</h4>\n          \n          <!-- 学段选择 -->\n          <el-form-item prop=\"gradeLevel\">\n            <el-select v-model=\"registerForm.gradeLevel\" placeholder=\"选择学段\" style=\"width: 100%\">\n              <el-option-group label=\"小学\">\n                <el-option label=\"小学1年级\" value=\"primary_1\"></el-option>\n                <el-option label=\"小学2年级\" value=\"primary_2\"></el-option>\n                <el-option label=\"小学3年级\" value=\"primary_3\"></el-option>\n                <el-option label=\"小学4年级\" value=\"primary_4\"></el-option>\n                <el-option label=\"小学5年级\" value=\"primary_5\"></el-option>\n                <el-option label=\"小学6年级\" value=\"primary_6\"></el-option>\n              </el-option-group>\n              <el-option-group label=\"初中\">\n                <el-option label=\"初中1年级\" value=\"junior_1\"></el-option>\n                <el-option label=\"初中2年级\" value=\"junior_2\"></el-option>\n                <el-option label=\"初中3年级\" value=\"junior_3\"></el-option>\n              </el-option-group>\n              <el-option-group label=\"高中\">\n                <el-option label=\"高中1年级\" value=\"senior_1\"></el-option>\n                <el-option label=\"高中2年级\" value=\"senior_2\"></el-option>\n                <el-option label=\"高中3年级\" value=\"senior_3\"></el-option>\n              </el-option-group>\n            </el-select>\n          </el-form-item>\n\n          <!-- 主学科选择 -->\n          <el-form-item prop=\"primarySubject\">\n            <el-select v-model=\"registerForm.primarySubject\" placeholder=\"选择主学科（必选1个）\" style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 副学科选择 -->\n          <el-form-item>\n            <el-select v-model=\"registerForm.secondarySubjects\" placeholder=\"选择副学科（可选多个）\" multiple style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 会员类型选择 -->\n          <el-form-item>\n            <el-radio-group v-model=\"registerForm.memberType\">\n              <el-radio label=\"free_trial\">\n                <span class=\"member-option\">\n                  <strong>免费体验</strong>\n                  <small>（7天免费，限1个学科）</small>\n                </span>\n              </el-radio>\n              <el-radio label=\"basic\">\n                <span class=\"member-option\">\n                  <strong>基础会员</strong>\n                  <small>（支持多学科学习）</small>\n                </span>\n              </el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 学校信息（可选） -->\n          <el-form-item>\n            <el-input v-model=\"registerForm.schoolName\" placeholder=\"学校名称（可选）\">\n              <svg-icon slot=\"prefix\" icon-class=\"education\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n        </div>\n\n        <!-- 教师专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'teacher'\">\n          <h4 class=\"section-title\">教学信息</h4>\n          \n          <!-- 身份证号 -->\n          <el-form-item prop=\"idCard\">\n            <el-input\n              v-model=\"registerForm.idCard\"\n              placeholder=\"身份证号\"\n              maxlength=\"18\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"idcard\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教师资格证编号 -->\n          <el-form-item prop=\"teacherCertNumber\">\n            <el-input\n              v-model=\"registerForm.teacherCertNumber\"\n              placeholder=\"教师资格证编号\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"documentation\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教学学段 -->\n          <el-form-item prop=\"teachingGradeLevels\">\n            <el-select v-model=\"registerForm.teachingGradeLevels\" placeholder=\"选择教学学段（可多选）\" multiple style=\"width: 100%\">\n              <el-option label=\"小学\" value=\"primary\"></el-option>\n              <el-option label=\"初中\" value=\"junior\"></el-option>\n              <el-option label=\"高中\" value=\"senior\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 教学学科 -->\n          <el-form-item prop=\"teachingSubjects\">\n            <el-select v-model=\"registerForm.teachingSubjects\" placeholder=\"选择教学学科（限1-2个）\" multiple style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 任职学校 -->\n          <el-form-item>\n            <el-input v-model=\"registerForm.schoolName\" placeholder=\"任职学校（可选）\">\n              <svg-icon slot=\"prefix\" icon-class=\"education\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教师资格证上传 -->\n          <el-form-item>\n            <el-upload\n              class=\"cert-upload\"\n              drag\n              action=\"#\"\n              :auto-upload=\"false\"\n              :on-change=\"handleCertUpload\"\n              accept=\"image/*\"\n            >\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">将教师资格证照片拖到此处，或<em>点击上传</em></div>\n              <div class=\"el-upload__tip\" slot=\"tip\">只能上传jpg/png文件，且不超过2MB</div>\n            </el-upload>\n          </el-form-item>\n        </div>\n\n        <!-- 家长专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'parent'\">\n          <h4 class=\"section-title\">权限设置</h4>\n          \n          <!-- 权限级别选择 -->\n          <el-form-item>\n            <el-radio-group v-model=\"registerForm.permissionLevel\">\n              <el-radio label=\"view_only\">仅查看学习进度</el-radio>\n              <el-radio label=\"view_notify\">查看进度 + 接收通知</el-radio>\n              <el-radio label=\"view_communicate\">查看进度 + 沟通老师</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </div>\n\n        <!-- 验证码 -->\n        <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n          <el-input\n            v-model=\"registerForm.code\"\n            auto-complete=\"off\"\n            placeholder=\"验证码\"\n            style=\"width: 63%\"\n          >\n            <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n          </el-input>\n          <div class=\"register-code\">\n            <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\n          </div>\n        </el-form-item>\n\n        <!-- 注册按钮 -->\n        <el-form-item style=\"width:100%;\">\n          <el-button\n            :loading=\"loading\"\n            size=\"medium\"\n            type=\"primary\"\n            style=\"width:100%;\"\n            @click.native.prevent=\"handleRegister\"\n          >\n            <span v-if=\"!loading\">{{ getRegisterButtonText() }}</span>\n            <span v-else>注 册 中...</span>\n          </el-button>\n        </el-form-item>\n\n        <!-- 登录链接 -->\n        <div class=\"login-link\">\n          <router-link class=\"link-type\" to=\"/edu-login\">已有账户？立即登录</router-link>\n        </div>\n      </el-form>\n    </div>\n\n    <!-- 底部版权 -->\n    <div class=\"el-register-footer\">\n      <span>Copyright © 2018-2025 lingdu.edu All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg } from \"@/api/login\"\nimport { registerStudent, registerTeacher, registerParent } from \"@/api/edu\"\n\nexport default {\n  name: \"EduRegister\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n\n    const validatePhoneNumber = (rule, value, callback) => {\n      if (!value) {\n        callback(new Error('请输入手机号'))\n      } else if (!/^1[3-9]\\d{9}$/.test(value)) {\n        callback(new Error('请输入正确的手机号'))\n      } else {\n        callback()\n      }\n    }\n\n    const validateIdCard = (rule, value, callback) => {\n      if (!value) {\n        callback(new Error('请输入身份证号'))\n      } else if (!/^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/.test(value)) {\n        callback(new Error('请输入正确的身份证号'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      activeRole: 'student',\n      codeUrl: \"\",\n      registerForm: {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        realName: \"\",\n        code: \"\",\n        uuid: \"\",\n        // 学生专用字段\n        gradeLevel: \"\",\n        primarySubject: \"\",\n        secondarySubjects: [],\n        memberType: \"free_trial\",\n        schoolName: \"\",\n        className: \"\",\n        idCard: \"\",\n        // 教师专用字段\n        teacherCertNumber: \"\",\n        teachingGradeLevels: [],\n        teachingSubjects: [],\n        teacherCertImage: \"\",\n        // 家长专用字段\n        permissionLevel: \"view_only\"\n      },\n      registerRules: {\n        username: [\n          { required: true, trigger: \"blur\", validator: validatePhoneNumber }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入密码\" },\n          { min: 5, max: 20, message: \"密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        realName: [\n          { required: true, trigger: \"blur\", message: \"请输入真实姓名\" },\n          { min: 2, max: 30, message: \"姓名长度必须介于 2 和 30 之间\", trigger: \"blur\" }\n        ],\n        gradeLevel: [\n          { required: true, trigger: \"change\", message: \"请选择学段\" }\n        ],\n        primarySubject: [\n          { required: true, trigger: \"change\", message: \"请选择主学科\" }\n        ],\n        idCard: [\n          { validator: validateIdCard, trigger: \"blur\" }\n        ],\n        teacherCertNumber: [\n          { required: true, trigger: \"blur\", message: \"请输入教师资格证编号\" }\n        ],\n        teachingGradeLevels: [\n          { required: true, trigger: \"change\", message: \"请选择教学学段\" }\n        ],\n        teachingSubjects: [\n          { required: true, trigger: \"change\", message: \"请选择教学学科\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n    // 从路由参数获取角色\n    if (this.$route.query.role) {\n      this.activeRole = this.$route.query.role\n    }\n  },\n  methods: {\n    // 角色切换\n    handleRoleChange(tab) {\n      this.activeRole = tab.name\n      this.resetForm()\n    },\n\n    // 获取角色标题\n    getRoleTitle() {\n      const titles = {\n        student: '学生注册',\n        parent: '家长注册',\n        teacher: '教师注册'\n      }\n      return titles[this.activeRole] || '用户注册'\n    },\n\n    // 获取注册按钮文本\n    getRegisterButtonText() {\n      const texts = {\n        student: '注册学生账号',\n        parent: '注册家长账号',\n        teacher: '提交教师认证'\n      }\n      return texts[this.activeRole] || '立即注册'\n    },\n\n    // 获取验证码\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n\n    // 处理教师资格证上传\n    handleCertUpload(file) {\n      const reader = new FileReader()\n      reader.onload = (e) => {\n        this.registerForm.teacherCertImage = e.target.result\n      }\n      reader.readAsDataURL(file.raw)\n    },\n\n    // 重置表单\n    resetForm() {\n      this.$refs.registerForm.resetFields()\n      this.registerForm = {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        realName: \"\",\n        code: \"\",\n        uuid: this.registerForm.uuid,\n        gradeLevel: \"\",\n        primarySubject: \"\",\n        secondarySubjects: [],\n        memberType: \"free_trial\",\n        schoolName: \"\",\n        className: \"\",\n        idCard: \"\",\n        teacherCertNumber: \"\",\n        teachingGradeLevels: [],\n        teachingSubjects: [],\n        teacherCertImage: \"\",\n        permissionLevel: \"view_only\"\n      }\n    },\n\n    // 处理注册\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          // 教师学科数量限制\n          if (this.activeRole === 'teacher' && this.registerForm.teachingSubjects.length > 2) {\n            this.$message.error('教学学科最多选择2个')\n            return\n          }\n\n          this.loading = true\n\n          // 根据角色调用不同的注册接口\n          let registerPromise\n          const formData = { ...this.registerForm }\n\n          // 处理数组字段\n          if (formData.secondarySubjects && Array.isArray(formData.secondarySubjects)) {\n            formData.secondarySubjects = formData.secondarySubjects.join(',')\n          }\n          if (formData.teachingGradeLevels && Array.isArray(formData.teachingGradeLevels)) {\n            formData.teachingGradeLevels = formData.teachingGradeLevels.join(',')\n          }\n          if (formData.teachingSubjects && Array.isArray(formData.teachingSubjects)) {\n            formData.teachingSubjects = formData.teachingSubjects.join(',')\n          }\n\n          switch (this.activeRole) {\n            case 'student':\n              registerPromise = registerStudent(formData)\n              break\n            case 'teacher':\n              registerPromise = registerTeacher(formData)\n              break\n            case 'parent':\n              registerPromise = registerParent(formData)\n              break\n            default:\n              this.$message.error('无效的注册类型')\n              this.loading = false\n              return\n          }\n\n          registerPromise.then(res => {\n            this.loading = false\n            const username = this.registerForm.username\n            let message = ''\n\n            switch (this.activeRole) {\n              case 'student':\n                message = `恭喜你，学生账号 ${username} 注册成功！`\n                break\n              case 'teacher':\n                message = `恭喜你，教师账号 ${username} 注册成功！您的资格正在审核中，审核通过后即可使用。`\n                break\n              case 'parent':\n                message = `恭喜你，家长账号 ${username} 注册成功！请登录后关联您的孩子。`\n                break\n            }\n\n            this.$alert(message, '注册成功', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/edu-login\")\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.edu-register {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px 0;\n}\n\n.register-container {\n  background: #ffffff;\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  width: 500px;\n  max-width: 90vw;\n  padding: 0;\n  overflow: hidden;\n}\n\n.role-tabs {\n  .el-tabs__header {\n    margin: 0;\n    background: #f8f9fa;\n  }\n\n  .el-tabs__nav-wrap {\n    padding: 0;\n  }\n\n  .el-tabs__item {\n    height: 60px;\n    line-height: 60px;\n    font-size: 16px;\n    font-weight: 500;\n\n    &.is-active {\n      color: #409EFF;\n      background: #ffffff;\n    }\n  }\n\n  .role-icon {\n    text-align: center;\n    padding: 10px 0;\n\n    i {\n      font-size: 24px;\n      color: #909399;\n    }\n  }\n\n  .el-tabs__item.is-active .role-icon i {\n    color: #409EFF;\n  }\n}\n\n.register-form {\n  padding: 30px 40px 40px;\n  max-height: 70vh;\n  overflow-y: auto;\n\n  .title {\n    margin: 0 0 30px 0;\n    text-align: center;\n    color: #303133;\n    font-weight: bold;\n    font-size: 22px;\n  }\n\n  .form-section {\n    margin-bottom: 25px;\n\n    .section-title {\n      color: #409EFF;\n      font-size: 16px;\n      margin-bottom: 15px;\n      padding-bottom: 8px;\n      border-bottom: 1px solid #EBEEF5;\n    }\n  }\n\n  .el-input {\n    height: 40px;\n    input {\n      height: 40px;\n      border-radius: 6px;\n    }\n  }\n\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n\n  .member-option {\n    display: block;\n\n    small {\n      display: block;\n      color: #909399;\n      font-size: 12px;\n      margin-top: 2px;\n    }\n  }\n\n  .cert-upload {\n    .el-upload {\n      width: 100%;\n    }\n\n    .el-upload-dragger {\n      width: 100%;\n      height: 120px;\n    }\n  }\n\n  .register-code {\n    width: 35%;\n    height: 40px;\n    float: right;\n    img {\n      cursor: pointer;\n      vertical-align: middle;\n      height: 40px;\n      border-radius: 6px;\n    }\n  }\n\n  .login-link {\n    text-align: center;\n    margin-top: 20px;\n\n    .link-type {\n      color: #409EFF;\n      text-decoration: none;\n\n      &:hover {\n        color: #66b1ff;\n      }\n    }\n  }\n}\n\n.el-register-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;AAmSA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,KAAA,CAAAK,YAAA,CAAAC,QAAA,KAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IAEA,IAAAI,mBAAA,YAAAA,oBAAAN,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA,4BAAAE,IAAA,CAAAN,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IAEA,IAAAM,cAAA,YAAAA,eAAAR,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA,mGAAAE,IAAA,CAAAN,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IAEA;MACAO,UAAA;MACAC,OAAA;MACAP,YAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,eAAA;QACAC,QAAA;QACAC,IAAA;QACAC,IAAA;QACA;QACAC,UAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACA;QACAC,iBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,gBAAA;QACA;QACAC,eAAA;MACA;MACAC,aAAA;QACAjB,QAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,SAAA,EAAAzB;QAAA,EACA;QACAF,QAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAF,OAAA;UAAAF,OAAA;QAAA,GACA;UAAAK,OAAA;UAAAH,OAAA;UAAAF,OAAA;QAAA,EACA;QACAlB,eAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,GACA;UAAAH,QAAA;UAAAE,SAAA,EAAAhC,eAAA;UAAA+B,OAAA;QAAA,EACA;QACAjB,QAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAF,OAAA;UAAAF,OAAA;QAAA,EACA;QACAd,UAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAf,cAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAV,MAAA,GACA;UAAAS,SAAA,EAAAvB,cAAA;UAAAsB,OAAA;QAAA,EACA;QACAP,iBAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAR,mBAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAP,gBAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA,EACA;QACAlB,IAAA;UAAAe,QAAA;UAAAC,OAAA;UAAAE,OAAA;QAAA;MACA;MACAI,OAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA;MACA,KAAAjC,UAAA,QAAA+B,MAAA,CAAAC,KAAA,CAAAC,IAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAApC,UAAA,GAAAoC,GAAA,CAAAjD,IAAA;MACA,KAAAkD,SAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA;MACA,IAAAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,OAAAH,MAAA,MAAAvC,UAAA;IACA;IAEA;IACA2C,qBAAA,WAAAA,sBAAA;MACA,IAAAC,KAAA;QACAJ,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,OAAAE,KAAA,MAAA5C,UAAA;IACA;IAEA;IACA8B,OAAA,WAAAA,QAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAjB,cAAA,GAAAoB,GAAA,CAAApB,cAAA,KAAAqB,SAAA,UAAAD,GAAA,CAAApB,cAAA;QACA,IAAAiB,MAAA,CAAAjB,cAAA;UACAiB,MAAA,CAAA5C,OAAA,8BAAA+C,GAAA,CAAAE,GAAA;UACAL,MAAA,CAAAnD,YAAA,CAAAY,IAAA,GAAA0C,GAAA,CAAA1C,IAAA;QACA;MACA;IACA;IAEA;IACA6C,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,MAAA,OAAAC,UAAA;MACAD,MAAA,CAAAE,MAAA,aAAAC,CAAA;QACAJ,MAAA,CAAA3D,YAAA,CAAAuB,gBAAA,GAAAwC,CAAA,CAAAC,MAAA,CAAAC,MAAA;MACA;MACAL,MAAA,CAAAM,aAAA,CAAAR,IAAA,CAAAS,GAAA;IACA;IAEA;IACAxB,SAAA,WAAAA,UAAA;MACA,KAAAyB,KAAA,CAAApE,YAAA,CAAAqE,WAAA;MACA,KAAArE,YAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,eAAA;QACAC,QAAA;QACAC,IAAA;QACAC,IAAA,OAAAZ,YAAA,CAAAY,IAAA;QACAC,UAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,eAAA;MACA;IACA;IAEA;IACA8C,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA,CAAApE,YAAA,CAAAwE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,MAAA,CAAAjE,UAAA,kBAAAiE,MAAA,CAAAvE,YAAA,CAAAsB,gBAAA,CAAAoD,MAAA;YACAH,MAAA,CAAAI,QAAA,CAAAC,KAAA;YACA;UACA;UAEAL,MAAA,CAAAtC,OAAA;;UAEA;UACA,IAAA4C,eAAA;UACA,IAAAC,QAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAT,MAAA,CAAAvE,YAAA;;UAEA;UACA,IAAA8E,QAAA,CAAA/D,iBAAA,IAAAkE,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAA/D,iBAAA;YACA+D,QAAA,CAAA/D,iBAAA,GAAA+D,QAAA,CAAA/D,iBAAA,CAAAoE,IAAA;UACA;UACA,IAAAL,QAAA,CAAAzD,mBAAA,IAAA4D,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAzD,mBAAA;YACAyD,QAAA,CAAAzD,mBAAA,GAAAyD,QAAA,CAAAzD,mBAAA,CAAA8D,IAAA;UACA;UACA,IAAAL,QAAA,CAAAxD,gBAAA,IAAA2D,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAxD,gBAAA;YACAwD,QAAA,CAAAxD,gBAAA,GAAAwD,QAAA,CAAAxD,gBAAA,CAAA6D,IAAA;UACA;UAEA,QAAAZ,MAAA,CAAAjE,UAAA;YACA;cACAuE,eAAA,OAAAO,oBAAA,EAAAN,QAAA;cACA;YACA;cACAD,eAAA,OAAAQ,oBAAA,EAAAP,QAAA;cACA;YACA;cACAD,eAAA,OAAAS,mBAAA,EAAAR,QAAA;cACA;YACA;cACAP,MAAA,CAAAI,QAAA,CAAAC,KAAA;cACAL,MAAA,CAAAtC,OAAA;cACA;UACA;UAEA4C,eAAA,CAAAxB,IAAA,WAAAC,GAAA;YACAiB,MAAA,CAAAtC,OAAA;YACA,IAAAzB,QAAA,GAAA+D,MAAA,CAAAvE,YAAA,CAAAQ,QAAA;YACA,IAAAqB,OAAA;YAEA,QAAA0C,MAAA,CAAAjE,UAAA;cACA;gBACAuB,OAAA,uDAAA0D,MAAA,CAAA/E,QAAA;gBACA;cACA;gBACAqB,OAAA,uDAAA0D,MAAA,CAAA/E,QAAA;gBACA;cACA;gBACAqB,OAAA,uDAAA0D,MAAA,CAAA/E,QAAA;gBACA;YACA;YAEA+D,MAAA,CAAAiB,MAAA,CAAA3D,OAAA;cACA4D,wBAAA;cACAC,IAAA;YACA,GAAArC,IAAA;cACAkB,MAAA,CAAAoB,OAAA,CAAAC,IAAA;YACA,GAAAC,KAAA;UACA,GAAAA,KAAA;YACAtB,MAAA,CAAAtC,OAAA;YACA,IAAAsC,MAAA,CAAArC,cAAA;cACAqC,MAAA,CAAAnC,OAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}