package com.lingdu.web.controller.system;

import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.lingdu.common.core.controller.BaseController;
import com.lingdu.common.core.domain.AjaxResult;
import com.lingdu.common.core.domain.model.StudentRegisterBody;
import com.lingdu.common.core.domain.model.TeacherRegisterBody;
import com.lingdu.common.core.domain.model.ParentRegisterBody;
import com.lingdu.common.core.domain.model.ParentBindStudentBody;
import com.lingdu.common.utils.StringUtils;
import com.lingdu.framework.web.service.EduRegisterService;
import com.lingdu.system.service.ISysConfigService;

/**
 * 教育系统分角色注册控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/edu")
public class EduRegisterController extends BaseController
{
    @Autowired
    private EduRegisterService eduRegisterService;

    @Autowired
    private ISysConfigService configService;

    /**
     * 学生注册
     */
    @PostMapping("/register/student")
    public AjaxResult registerStudent(@Valid @RequestBody StudentRegisterBody registerBody)
    {
        if (!isRegisterEnabled())
        {
            return error("当前系统没有开启注册功能！");
        }
        
        String msg = eduRegisterService.registerStudent(registerBody);
        if (StringUtils.isEmpty(msg))
        {
            return success("学生注册成功！请登录开始学习。");
        }
        else
        {
            return error(msg);
        }
    }

    /**
     * 教师注册
     */
    @PostMapping("/register/teacher")
    public AjaxResult registerTeacher(@Valid @RequestBody TeacherRegisterBody registerBody)
    {
        if (!isRegisterEnabled())
        {
            return error("当前系统没有开启注册功能！");
        }
        
        String msg = eduRegisterService.registerTeacher(registerBody);
        if (StringUtils.isEmpty(msg))
        {
            return success("教师注册成功！您的账号正在审核中，审核通过后即可使用。");
        }
        else
        {
            return error(msg);
        }
    }

    /**
     * 家长注册
     */
    @PostMapping("/register/parent")
    public AjaxResult registerParent(@Valid @RequestBody ParentRegisterBody registerBody)
    {
        if (!isRegisterEnabled())
        {
            return error("当前系统没有开启注册功能！");
        }
        
        String msg = eduRegisterService.registerParent(registerBody);
        if (StringUtils.isEmpty(msg))
        {
            return success("家长注册成功！请登录后关联您的孩子。");
        }
        else
        {
            return error(msg);
        }
    }

    /**
     * 发送短信验证码
     */
    @PostMapping("/sms/send")
    public AjaxResult sendSmsCode(@RequestParam String phoneNumber, @RequestParam String verifyType)
    {
        // 验证参数
        if (StringUtils.isEmpty(phoneNumber))
        {
            return error("手机号不能为空");
        }
        if (StringUtils.isEmpty(verifyType))
        {
            return error("验证类型不能为空");
        }
        
        // 验证类型必须是指定的几种
        if (!"register".equals(verifyType) && !"login".equals(verifyType) && !"bind".equals(verifyType))
        {
            return error("无效的验证类型");
        }
        
        String msg = eduRegisterService.sendSmsCode(phoneNumber, verifyType);
        if (StringUtils.isEmpty(msg))
        {
            return success("验证码发送成功，请注意查收");
        }
        else
        {
            return error(msg);
        }
    }

    /**
     * 验证短信验证码
     */
    @PostMapping("/sms/verify")
    public AjaxResult verifySmsCode(@RequestParam String phoneNumber, 
                                   @RequestParam String verifyCode, 
                                   @RequestParam String verifyType)
    {
        boolean isValid = eduRegisterService.verifySmsCode(phoneNumber, verifyCode, verifyType);
        if (isValid)
        {
            return success("验证码验证成功");
        }
        else
        {
            return error("验证码错误或已过期");
        }
    }

    /**
     * 家长关联学生
     */
    @PostMapping("/parent/bind-student")
    public AjaxResult bindStudent(@Valid @RequestBody ParentBindStudentBody bindBody)
    {
        // TODO: 实现家长关联学生的逻辑
        // 1. 验证学生信息是否存在
        // 2. 验证验证码
        // 3. 创建关联关系
        // 4. 发送通知给学生
        
        return success("关联申请已发送，等待学生确认");
    }

    /**
     * 获取学段列表
     */
    @GetMapping("/dict/grade-levels")
    public AjaxResult getGradeLevels()
    {
        // TODO: 从字典服务获取学段列表
        return success("获取成功");
    }

    /**
     * 获取学科列表
     */
    @GetMapping("/dict/subjects")
    public AjaxResult getSubjects()
    {
        // TODO: 从字典服务获取学科列表
        return success("获取成功");
    }

    /**
     * 获取会员类型列表
     */
    @GetMapping("/dict/member-types")
    public AjaxResult getMemberTypes()
    {
        // TODO: 从字典服务获取会员类型列表
        return success("获取成功");
    }

    /**
     * 检查注册功能是否开启
     */
    private boolean isRegisterEnabled()
    {
        return "true".equals(configService.selectConfigByKey("sys.account.registerUser"));
    }
}
