{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\help\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\help\\index.vue", "mtime": 1758178462824}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1757925919067}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9zdHVkeS9ydW95aS9saW5nZHUvbGluZ2R1L2xpbmdkdS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9TaXRlRm9vdGVyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL2NvbXBvbmVudHMvU2l0ZUZvb3RlciIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdIZWxwUGFnZScsCiAgY29tcG9uZW50czogewogICAgU2l0ZUZvb3RlcjogX1NpdGVGb290ZXIuZGVmYXVsdAogIH0KfTs="}, {"version": 3, "names": ["_SiteFooter", "_interopRequireDefault", "require", "name", "components", "SiteFooter"], "sources": ["src/views/help/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"help-page\">\r\n        <el-card class=\"hero\" shadow=\"never\">\r\n            <h1 class=\"title\">帮助中心</h1>\r\n            <p class=\"subtitle\">常见问题与使用指南正在完善中。</p>\r\n        </el-card>\r\n        <site-footer />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport SiteFooter from '@/components/SiteFooter'\r\nexport default {\r\n    name: 'HelpPage',\r\n    components: { SiteFooter }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.help-page {\r\n    padding: 20px;\r\n}\r\n\r\n.hero {\r\n    margin: 0 auto;\r\n    max-width: 1100px;\r\n    border: none;\r\n    background: #f5f7fa;\r\n}\r\n\r\n.title {\r\n    margin: 0;\r\n    padding: 16px 8px 4px;\r\n    font-size: 22px;\r\n    font-weight: 700;\r\n    text-align: center;\r\n}\r\n\r\n.subtitle {\r\n    margin: 0 0 16px;\r\n    color: #909399;\r\n    text-align: center;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAWA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;AACA", "ignoreList": []}]}