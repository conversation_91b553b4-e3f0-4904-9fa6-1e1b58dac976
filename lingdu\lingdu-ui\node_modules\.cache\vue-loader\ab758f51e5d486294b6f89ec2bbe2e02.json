{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-register.vue?vue&type=style&index=0&id=5421b218&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-register.vue", "mtime": 1758185596148}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757926224263}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757926225750}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757926224811}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757926223774}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZWR1LXJlZ2lzdGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWluLWhlaWdodDogMTAwdmg7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsKICBwYWRkaW5nOiAyMHB4IDA7Cn0KCi5yZWdpc3Rlci1jb250YWluZXIgewogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKICBib3gtc2hhZG93OiAwIDE1cHggMzVweCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgd2lkdGg6IDUwMHB4OwogIG1heC13aWR0aDogOTB2dzsKICBwYWRkaW5nOiAwOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5yb2xlLXRhYnMgewogIC5lbC10YWJzX19oZWFkZXIgewogICAgbWFyZ2luOiAwOwogICAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICB9CgogIC5lbC10YWJzX19uYXYtd3JhcCB7CiAgICBwYWRkaW5nOiAwOwogIH0KCiAgLmVsLXRhYnNfX2l0ZW0gewogICAgaGVpZ2h0OiA2MHB4OwogICAgbGluZS1oZWlnaHQ6IDYwcHg7CiAgICBmb250LXNpemU6IDE2cHg7CiAgICBmb250LXdlaWdodDogNTAwOwoKICAgICYuaXMtYWN0aXZlIHsKICAgICAgY29sb3I6ICM0MDlFRkY7CiAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgICB9CiAgfQoKICAucm9sZS1pY29uIHsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIHBhZGRpbmc6IDEwcHggMDsKCiAgICBpIHsKICAgICAgZm9udC1zaXplOiAyNHB4OwogICAgICBjb2xvcjogIzkwOTM5OTsKICAgIH0KICB9CgogIC5lbC10YWJzX19pdGVtLmlzLWFjdGl2ZSAucm9sZS1pY29uIGkgewogICAgY29sb3I6ICM0MDlFRkY7CiAgfQp9CgoucmVnaXN0ZXItZm9ybSB7CiAgcGFkZGluZzogMzBweCA0MHB4IDQwcHg7CiAgbWF4LWhlaWdodDogNzB2aDsKICBvdmVyZmxvdy15OiBhdXRvOwoKICAudGl0bGUgewogICAgbWFyZ2luOiAwIDAgMzBweCAwOwogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgY29sb3I6ICMzMDMxMzM7CiAgICBmb250LXdlaWdodDogYm9sZDsKICAgIGZvbnQtc2l6ZTogMjJweDsKICB9CgogIC5mb3JtLXNlY3Rpb24gewogICAgbWFyZ2luLWJvdHRvbTogMjVweDsKCiAgICAuc2VjdGlvbi10aXRsZSB7CiAgICAgIGNvbG9yOiAjNDA5RUZGOwogICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgICAgIHBhZGRpbmctYm90dG9tOiA4cHg7CiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjRUJFRUY1OwogICAgfQogIH0KCiAgLmVsLWlucHV0IHsKICAgIGhlaWdodDogNDBweDsKICAgIGlucHV0IHsKICAgICAgaGVpZ2h0OiA0MHB4OwogICAgICBib3JkZXItcmFkaXVzOiA2cHg7CiAgICB9CiAgfQoKICAuaW5wdXQtaWNvbiB7CiAgICBoZWlnaHQ6IDM5cHg7CiAgICB3aWR0aDogMTRweDsKICAgIG1hcmdpbi1sZWZ0OiAycHg7CiAgfQoKICAubWVtYmVyLW9wdGlvbiB7CiAgICBkaXNwbGF5OiBibG9jazsKCiAgICBzbWFsbCB7CiAgICAgIGRpc3BsYXk6IGJsb2NrOwogICAgICBjb2xvcjogIzkwOTM5OTsKICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICBtYXJnaW4tdG9wOiAycHg7CiAgICB9CiAgfQoKICAuY2VydC11cGxvYWQgewogICAgLmVsLXVwbG9hZCB7CiAgICAgIHdpZHRoOiAxMDAlOwogICAgfQoKICAgIC5lbC11cGxvYWQtZHJhZ2dlciB7CiAgICAgIHdpZHRoOiAxMDAlOwogICAgICBoZWlnaHQ6IDEyMHB4OwogICAgfQogIH0KCiAgLnJlZ2lzdGVyLWNvZGUgewogICAgd2lkdGg6IDM1JTsKICAgIGhlaWdodDogNDBweDsKICAgIGZsb2F0OiByaWdodDsKICAgIGltZyB7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKICAgICAgaGVpZ2h0OiA0MHB4OwogICAgICBib3JkZXItcmFkaXVzOiA2cHg7CiAgICB9CiAgfQoKICAubG9naW4tbGluayB7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBtYXJnaW4tdG9wOiAyMHB4OwoKICAgIC5saW5rLXR5cGUgewogICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgY29sb3I6ICM2NmIxZmY7CiAgICAgIH0KICAgIH0KICB9Cn0KCi5lbC1yZWdpc3Rlci1mb290ZXIgewogIGhlaWdodDogNDBweDsKICBsaW5lLWhlaWdodDogNDBweDsKICBwb3NpdGlvbjogZml4ZWQ7CiAgYm90dG9tOiAwOwogIHdpZHRoOiAxMDAlOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogI2ZmZjsKICBmb250LWZhbWlseTogQXJpYWw7CiAgZm9udC1zaXplOiAxMnB4OwogIGxldHRlci1zcGFjaW5nOiAxcHg7Cn0K"}, {"version": 3, "sources": ["edu-register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "edu-register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"edu-register\">\n    <div class=\"register-container\">\n      <!-- 角色选择标签页 -->\n      <el-tabs v-model=\"activeRole\" class=\"role-tabs\" @tab-click=\"handleRoleChange\">\n        <el-tab-pane label=\"学生注册\" name=\"student\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-user-solid\"></i>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"家长注册\" name=\"parent\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-user\"></i>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"教师注册\" name=\"teacher\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-s-custom\"></i>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <!-- 注册表单 -->\n      <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n        <h3 class=\"title\">{{ getRoleTitle() }}</h3>\n\n        <!-- 基础信息 -->\n        <div class=\"form-section\">\n          <h4 class=\"section-title\">基础信息</h4>\n          \n          <!-- 手机号 -->\n          <el-form-item prop=\"username\">\n            <el-input\n              v-model=\"registerForm.username\"\n              placeholder=\"手机号（用于登录）\"\n              maxlength=\"11\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"phone\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 密码 -->\n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"registerForm.password\"\n              type=\"password\"\n              placeholder=\"设置登录密码\"\n              show-password\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 确认密码 -->\n          <el-form-item prop=\"confirmPassword\">\n            <el-input\n              v-model=\"registerForm.confirmPassword\"\n              type=\"password\"\n              placeholder=\"确认登录密码\"\n              show-password\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 真实姓名 -->\n          <el-form-item prop=\"realName\">\n            <el-input\n              v-model=\"registerForm.realName\"\n              placeholder=\"真实姓名\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n        </div>\n\n        <!-- 学生专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'student'\">\n          <h4 class=\"section-title\">学习信息</h4>\n          \n          <!-- 学段选择 -->\n          <el-form-item prop=\"gradeLevel\">\n            <el-select v-model=\"registerForm.gradeLevel\" placeholder=\"选择学段\" style=\"width: 100%\">\n              <el-option-group label=\"小学\">\n                <el-option label=\"小学1年级\" value=\"primary_1\"></el-option>\n                <el-option label=\"小学2年级\" value=\"primary_2\"></el-option>\n                <el-option label=\"小学3年级\" value=\"primary_3\"></el-option>\n                <el-option label=\"小学4年级\" value=\"primary_4\"></el-option>\n                <el-option label=\"小学5年级\" value=\"primary_5\"></el-option>\n                <el-option label=\"小学6年级\" value=\"primary_6\"></el-option>\n              </el-option-group>\n              <el-option-group label=\"初中\">\n                <el-option label=\"初中1年级\" value=\"junior_1\"></el-option>\n                <el-option label=\"初中2年级\" value=\"junior_2\"></el-option>\n                <el-option label=\"初中3年级\" value=\"junior_3\"></el-option>\n              </el-option-group>\n              <el-option-group label=\"高中\">\n                <el-option label=\"高中1年级\" value=\"senior_1\"></el-option>\n                <el-option label=\"高中2年级\" value=\"senior_2\"></el-option>\n                <el-option label=\"高中3年级\" value=\"senior_3\"></el-option>\n              </el-option-group>\n            </el-select>\n          </el-form-item>\n\n          <!-- 主学科选择 -->\n          <el-form-item prop=\"primarySubject\">\n            <el-select v-model=\"registerForm.primarySubject\" placeholder=\"选择主学科（必选1个）\" style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 副学科选择 -->\n          <el-form-item>\n            <el-select v-model=\"registerForm.secondarySubjects\" placeholder=\"选择副学科（可选多个）\" multiple style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 会员类型选择 -->\n          <el-form-item>\n            <el-radio-group v-model=\"registerForm.memberType\">\n              <el-radio label=\"free_trial\">\n                <span class=\"member-option\">\n                  <strong>免费体验</strong>\n                  <small>（7天免费，限1个学科）</small>\n                </span>\n              </el-radio>\n              <el-radio label=\"basic\">\n                <span class=\"member-option\">\n                  <strong>基础会员</strong>\n                  <small>（支持多学科学习）</small>\n                </span>\n              </el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 学校信息（可选） -->\n          <el-form-item>\n            <el-input v-model=\"registerForm.schoolName\" placeholder=\"学校名称（可选）\">\n              <svg-icon slot=\"prefix\" icon-class=\"education\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n        </div>\n\n        <!-- 教师专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'teacher'\">\n          <h4 class=\"section-title\">教学信息</h4>\n          \n          <!-- 身份证号 -->\n          <el-form-item prop=\"idCard\">\n            <el-input\n              v-model=\"registerForm.idCard\"\n              placeholder=\"身份证号\"\n              maxlength=\"18\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"idcard\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教师资格证编号 -->\n          <el-form-item prop=\"teacherCertNumber\">\n            <el-input\n              v-model=\"registerForm.teacherCertNumber\"\n              placeholder=\"教师资格证编号\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"documentation\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教学学段 -->\n          <el-form-item prop=\"teachingGradeLevels\">\n            <el-select v-model=\"registerForm.teachingGradeLevels\" placeholder=\"选择教学学段（可多选）\" multiple style=\"width: 100%\">\n              <el-option label=\"小学\" value=\"primary\"></el-option>\n              <el-option label=\"初中\" value=\"junior\"></el-option>\n              <el-option label=\"高中\" value=\"senior\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 教学学科 -->\n          <el-form-item prop=\"teachingSubjects\">\n            <el-select v-model=\"registerForm.teachingSubjects\" placeholder=\"选择教学学科（限1-2个）\" multiple style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 任职学校 -->\n          <el-form-item>\n            <el-input v-model=\"registerForm.schoolName\" placeholder=\"任职学校（可选）\">\n              <svg-icon slot=\"prefix\" icon-class=\"education\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教师资格证上传 -->\n          <el-form-item>\n            <el-upload\n              class=\"cert-upload\"\n              drag\n              action=\"#\"\n              :auto-upload=\"false\"\n              :on-change=\"handleCertUpload\"\n              accept=\"image/*\"\n            >\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">将教师资格证照片拖到此处，或<em>点击上传</em></div>\n              <div class=\"el-upload__tip\" slot=\"tip\">只能上传jpg/png文件，且不超过2MB</div>\n            </el-upload>\n          </el-form-item>\n        </div>\n\n        <!-- 家长专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'parent'\">\n          <h4 class=\"section-title\">权限设置</h4>\n          \n          <!-- 权限级别选择 -->\n          <el-form-item>\n            <el-radio-group v-model=\"registerForm.permissionLevel\">\n              <el-radio label=\"view_only\">仅查看学习进度</el-radio>\n              <el-radio label=\"view_notify\">查看进度 + 接收通知</el-radio>\n              <el-radio label=\"view_communicate\">查看进度 + 沟通老师</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </div>\n\n        <!-- 验证码 -->\n        <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n          <el-input\n            v-model=\"registerForm.code\"\n            auto-complete=\"off\"\n            placeholder=\"验证码\"\n            style=\"width: 63%\"\n          >\n            <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n          </el-input>\n          <div class=\"register-code\">\n            <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\n          </div>\n        </el-form-item>\n\n        <!-- 注册按钮 -->\n        <el-form-item style=\"width:100%;\">\n          <el-button\n            :loading=\"loading\"\n            size=\"medium\"\n            type=\"primary\"\n            style=\"width:100%;\"\n            @click.native.prevent=\"handleRegister\"\n          >\n            <span v-if=\"!loading\">{{ getRegisterButtonText() }}</span>\n            <span v-else>注 册 中...</span>\n          </el-button>\n        </el-form-item>\n\n        <!-- 登录链接 -->\n        <div class=\"login-link\">\n          <router-link class=\"link-type\" to=\"/edu-login\">已有账户？立即登录</router-link>\n        </div>\n      </el-form>\n    </div>\n\n    <!-- 底部版权 -->\n    <div class=\"el-register-footer\">\n      <span>Copyright © 2018-2025 lingdu.edu All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg } from \"@/api/login\"\nimport { registerStudent, registerTeacher, registerParent } from \"@/api/edu\"\n\nexport default {\n  name: \"EduRegister\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n\n    const validatePhoneNumber = (rule, value, callback) => {\n      if (!value) {\n        callback(new Error('请输入手机号'))\n      } else if (!/^1[3-9]\\d{9}$/.test(value)) {\n        callback(new Error('请输入正确的手机号'))\n      } else {\n        callback()\n      }\n    }\n\n    const validateIdCard = (rule, value, callback) => {\n      if (!value) {\n        callback(new Error('请输入身份证号'))\n      } else if (!/^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/.test(value)) {\n        callback(new Error('请输入正确的身份证号'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      activeRole: 'student',\n      codeUrl: \"\",\n      registerForm: {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        realName: \"\",\n        code: \"\",\n        uuid: \"\",\n        // 学生专用字段\n        gradeLevel: \"\",\n        primarySubject: \"\",\n        secondarySubjects: [],\n        memberType: \"free_trial\",\n        schoolName: \"\",\n        className: \"\",\n        idCard: \"\",\n        // 教师专用字段\n        teacherCertNumber: \"\",\n        teachingGradeLevels: [],\n        teachingSubjects: [],\n        teacherCertImage: \"\",\n        // 家长专用字段\n        permissionLevel: \"view_only\"\n      },\n      registerRules: {\n        username: [\n          { required: true, trigger: \"blur\", validator: validatePhoneNumber }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入密码\" },\n          { min: 5, max: 20, message: \"密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        realName: [\n          { required: true, trigger: \"blur\", message: \"请输入真实姓名\" },\n          { min: 2, max: 30, message: \"姓名长度必须介于 2 和 30 之间\", trigger: \"blur\" }\n        ],\n        gradeLevel: [\n          { required: true, trigger: \"change\", message: \"请选择学段\" }\n        ],\n        primarySubject: [\n          { required: true, trigger: \"change\", message: \"请选择主学科\" }\n        ],\n        idCard: [\n          { validator: validateIdCard, trigger: \"blur\" }\n        ],\n        teacherCertNumber: [\n          { required: true, trigger: \"blur\", message: \"请输入教师资格证编号\" }\n        ],\n        teachingGradeLevels: [\n          { required: true, trigger: \"change\", message: \"请选择教学学段\" }\n        ],\n        teachingSubjects: [\n          { required: true, trigger: \"change\", message: \"请选择教学学科\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n    // 从路由参数获取角色\n    if (this.$route.query.role) {\n      this.activeRole = this.$route.query.role\n    }\n  },\n  methods: {\n    // 角色切换\n    handleRoleChange(tab) {\n      this.activeRole = tab.name\n      this.resetForm()\n    },\n\n    // 获取角色标题\n    getRoleTitle() {\n      const titles = {\n        student: '学生注册',\n        parent: '家长注册',\n        teacher: '教师注册'\n      }\n      return titles[this.activeRole] || '用户注册'\n    },\n\n    // 获取注册按钮文本\n    getRegisterButtonText() {\n      const texts = {\n        student: '注册学生账号',\n        parent: '注册家长账号',\n        teacher: '提交教师认证'\n      }\n      return texts[this.activeRole] || '立即注册'\n    },\n\n    // 获取验证码\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n\n    // 处理教师资格证上传\n    handleCertUpload(file) {\n      const reader = new FileReader()\n      reader.onload = (e) => {\n        this.registerForm.teacherCertImage = e.target.result\n      }\n      reader.readAsDataURL(file.raw)\n    },\n\n    // 重置表单\n    resetForm() {\n      this.$refs.registerForm.resetFields()\n      this.registerForm = {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        realName: \"\",\n        code: \"\",\n        uuid: this.registerForm.uuid,\n        gradeLevel: \"\",\n        primarySubject: \"\",\n        secondarySubjects: [],\n        memberType: \"free_trial\",\n        schoolName: \"\",\n        className: \"\",\n        idCard: \"\",\n        teacherCertNumber: \"\",\n        teachingGradeLevels: [],\n        teachingSubjects: [],\n        teacherCertImage: \"\",\n        permissionLevel: \"view_only\"\n      }\n    },\n\n    // 处理注册\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          // 教师学科数量限制\n          if (this.activeRole === 'teacher' && this.registerForm.teachingSubjects.length > 2) {\n            this.$message.error('教学学科最多选择2个')\n            return\n          }\n\n          this.loading = true\n\n          // 根据角色调用不同的注册接口\n          let registerPromise\n          const formData = { ...this.registerForm }\n\n          // 处理数组字段\n          if (formData.secondarySubjects && Array.isArray(formData.secondarySubjects)) {\n            formData.secondarySubjects = formData.secondarySubjects.join(',')\n          }\n          if (formData.teachingGradeLevels && Array.isArray(formData.teachingGradeLevels)) {\n            formData.teachingGradeLevels = formData.teachingGradeLevels.join(',')\n          }\n          if (formData.teachingSubjects && Array.isArray(formData.teachingSubjects)) {\n            formData.teachingSubjects = formData.teachingSubjects.join(',')\n          }\n\n          switch (this.activeRole) {\n            case 'student':\n              registerPromise = registerStudent(formData)\n              break\n            case 'teacher':\n              registerPromise = registerTeacher(formData)\n              break\n            case 'parent':\n              registerPromise = registerParent(formData)\n              break\n            default:\n              this.$message.error('无效的注册类型')\n              this.loading = false\n              return\n          }\n\n          registerPromise.then(res => {\n            this.loading = false\n            const username = this.registerForm.username\n            let message = ''\n\n            switch (this.activeRole) {\n              case 'student':\n                message = `恭喜你，学生账号 ${username} 注册成功！`\n                break\n              case 'teacher':\n                message = `恭喜你，教师账号 ${username} 注册成功！您的资格正在审核中，审核通过后即可使用。`\n                break\n              case 'parent':\n                message = `恭喜你，家长账号 ${username} 注册成功！请登录后关联您的孩子。`\n                break\n            }\n\n            this.$alert(message, '注册成功', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/edu-login\")\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.edu-register {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px 0;\n}\n\n.register-container {\n  background: #ffffff;\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  width: 500px;\n  max-width: 90vw;\n  padding: 0;\n  overflow: hidden;\n}\n\n.role-tabs {\n  .el-tabs__header {\n    margin: 0;\n    background: #f8f9fa;\n  }\n\n  .el-tabs__nav-wrap {\n    padding: 0;\n  }\n\n  .el-tabs__item {\n    height: 60px;\n    line-height: 60px;\n    font-size: 16px;\n    font-weight: 500;\n\n    &.is-active {\n      color: #409EFF;\n      background: #ffffff;\n    }\n  }\n\n  .role-icon {\n    text-align: center;\n    padding: 10px 0;\n\n    i {\n      font-size: 24px;\n      color: #909399;\n    }\n  }\n\n  .el-tabs__item.is-active .role-icon i {\n    color: #409EFF;\n  }\n}\n\n.register-form {\n  padding: 30px 40px 40px;\n  max-height: 70vh;\n  overflow-y: auto;\n\n  .title {\n    margin: 0 0 30px 0;\n    text-align: center;\n    color: #303133;\n    font-weight: bold;\n    font-size: 22px;\n  }\n\n  .form-section {\n    margin-bottom: 25px;\n\n    .section-title {\n      color: #409EFF;\n      font-size: 16px;\n      margin-bottom: 15px;\n      padding-bottom: 8px;\n      border-bottom: 1px solid #EBEEF5;\n    }\n  }\n\n  .el-input {\n    height: 40px;\n    input {\n      height: 40px;\n      border-radius: 6px;\n    }\n  }\n\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n\n  .member-option {\n    display: block;\n\n    small {\n      display: block;\n      color: #909399;\n      font-size: 12px;\n      margin-top: 2px;\n    }\n  }\n\n  .cert-upload {\n    .el-upload {\n      width: 100%;\n    }\n\n    .el-upload-dragger {\n      width: 100%;\n      height: 120px;\n    }\n  }\n\n  .register-code {\n    width: 35%;\n    height: 40px;\n    float: right;\n    img {\n      cursor: pointer;\n      vertical-align: middle;\n      height: 40px;\n      border-radius: 6px;\n    }\n  }\n\n  .login-link {\n    text-align: center;\n    margin-top: 20px;\n\n    .link-type {\n      color: #409EFF;\n      text-decoration: none;\n\n      &:hover {\n        color: #66b1ff;\n      }\n    }\n  }\n}\n\n.el-register-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n</style>\n"]}]}