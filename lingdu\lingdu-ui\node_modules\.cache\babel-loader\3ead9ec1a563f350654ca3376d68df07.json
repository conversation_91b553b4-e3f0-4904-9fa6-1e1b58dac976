{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\settings.js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\settings.js", "mtime": 1758178462819}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1757925919067}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSB7CiAgLyoqDQogICAqIOe9kemhteagh+mimA0KICAgKi8KICB0aXRsZTogcHJvY2Vzcy5lbnYuVlVFX0FQUF9USVRMRSwKICAvKioNCiAgICog5L6n6L655qCP5Li76aKYIOa3seiJsuS4u+mimHRoZW1lLWRhcmvvvIzmtYXoibLkuLvpoph0aGVtZS1saWdodA0KICAgKi8KICBzaWRlVGhlbWU6ICd0aGVtZS1kYXJrJywKICAvKioNCiAgICog57O757uf5biD5bGA6YWN572uDQogICAqLwogIHNob3dTZXR0aW5nczogdHJ1ZSwKICAvKioNCiAgICog5piv5ZCm5pi+56S66aG26YOo5a+86IiqDQogICAqLwogIHRvcE5hdjogZmFsc2UsCiAgLyoqDQogICAqIOaYr+WQpuaYvuekuiB0YWdzVmlldw0KICAgKi8KICB0YWdzVmlldzogZmFsc2UsCiAgLyoqDQogICAqIOaYvuekuumhteetvuWbvuaghw0KICAgKi8KICB0YWdzSWNvbjogZmFsc2UsCiAgLyoqDQogICAqIOaYr+WQpuWbuuWumuWktOmDqA0KICAgKi8KICBmaXhlZEhlYWRlcjogZmFsc2UsCiAgLyoqDQogICAqIOaYr+WQpuaYvuekumxvZ28NCiAgICovCiAgc2lkZWJhckxvZ286IHRydWUsCiAgLyoqDQogICAqIOaYr+WQpuaYvuekuuWKqOaAgeagh+mimA0KICAgKi8KICBkeW5hbWljVGl0bGU6IGZhbHNlLAogIC8qKg0KICAgKiDmmK/lkKbmmL7npLrlupXpg6jniYjmnYMNCiAgICovCiAgZm9vdGVyVmlzaWJsZTogZmFsc2UsCiAgLyoqDQogICAqIOW6lemDqOeJiOadg+aWh+acrOWGheWuuQ0KICAgKi8KICBmb290ZXJDb250ZW50OiAnQ29weXJpZ2h0IMKpIDIwMTgtMjAyNSBSdW9ZaS4gQWxsIFJpZ2h0cyBSZXNlcnZlZC4nCn07"}, {"version": 3, "names": ["module", "exports", "title", "process", "env", "VUE_APP_TITLE", "sideTheme", "showSettings", "topNav", "tagsView", "tagsIcon", "fixedHeader", "sidebarLogo", "dynamicTitle", "footerVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["E:/study/ruoyi/lingdu/lingdu/lingdu-ui/src/settings.js"], "sourcesContent": ["module.exports = {\r\n  /**\r\n   * 网页标题\r\n   */\r\n  title: process.env.VUE_APP_TITLE,\r\n\r\n  /**\r\n   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light\r\n   */\r\n  sideTheme: 'theme-dark',\r\n\r\n  /**\r\n   * 系统布局配置\r\n   */\r\n  showSettings: true,\r\n\r\n  /**\r\n   * 是否显示顶部导航\r\n   */\r\n  topNav: false,\r\n\r\n  /**\r\n   * 是否显示 tagsView\r\n   */\r\n  tagsView: false,\r\n  \r\n  /**\r\n   * 显示页签图标\r\n   */\r\n  tagsIcon: false,\r\n\r\n  /**\r\n   * 是否固定头部\r\n   */\r\n  fixedHeader: false,\r\n\r\n  /**\r\n   * 是否显示logo\r\n   */\r\n  sidebarLogo: true,\r\n\r\n  /**\r\n   * 是否显示动态标题\r\n   */\r\n  dynamicTitle: false,\r\n\r\n  /**\r\n   * 是否显示底部版权\r\n   */\r\n  footerVisible: false,\r\n\r\n  /**\r\n   * 底部版权文本内容\r\n   */\r\n  footerContent: 'Copyright © 2018-2025 RuoYi. All Rights Reserved.'\r\n}\r\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EACf;AACF;AACA;EACEC,KAAK,EAAEC,OAAO,CAACC,GAAG,CAACC,aAAa;EAEhC;AACF;AACA;EACEC,SAAS,EAAE,YAAY;EAEvB;AACF;AACA;EACEC,YAAY,EAAE,IAAI;EAElB;AACF;AACA;EACEC,MAAM,EAAE,KAAK;EAEb;AACF;AACA;EACEC,QAAQ,EAAE,KAAK;EAEf;AACF;AACA;EACEC,QAAQ,EAAE,KAAK;EAEf;AACF;AACA;EACEC,WAAW,EAAE,KAAK;EAElB;AACF;AACA;EACEC,WAAW,EAAE,IAAI;EAEjB;AACF;AACA;EACEC,YAAY,EAAE,KAAK;EAEnB;AACF;AACA;EACEC,aAAa,EAAE,KAAK;EAEpB;AACF;AACA;EACEC,aAAa,EAAE;AACjB,CAAC", "ignoreList": []}]}