{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\about\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\about\\index.vue", "mtime": 1758178462822}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgU2l0ZUZvb3RlciBmcm9tICdAL2NvbXBvbmVudHMvU2l0ZUZvb3RlcicNCmV4cG9ydCBkZWZhdWx0IHsNCiAgICBuYW1lOiAnQWJvdXRQYWdlJywNCiAgICBjb21wb25lbnRzOiB7IFNpdGVGb290ZXIgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/about", "sourcesContent": ["<template>\r\n    <div class=\"about-page\">\r\n        <el-card class=\"hero\" shadow=\"never\">\r\n            <div class=\"hero-content\">\r\n                <h1 class=\"title\">关于我们 · 灵渡空间文化传媒有限公司</h1>\r\n                <p class=\"subtitle\">让AI赋能教育，用科技点亮每一次学习。</p>\r\n            </div>\r\n        </el-card>\r\n\r\n        <div class=\"content\">\r\n            <el-row :gutter=\"24\">\r\n                <el-col :span=\"24\">\r\n                    <el-card shadow=\"hover\" class=\"section\">\r\n                        <h2 class=\"section-title\">公司简介</h2>\r\n                        <p class=\"para\">\r\n                            灵渡空间文化传媒有限公司专注于人工智能与教育的融合应用，致力于打造高效、个性化的学习辅助系统。\r\n                            我们联合一线优秀教师与技术专家团队，持续探索“AI × 教育”的创新路径，助力学生高效成长、教师提质增效、学校科学治理。\r\n                        </p>\r\n                    </el-card>\r\n                </el-col>\r\n\r\n                <el-col :span=\"24\">\r\n                    <el-card shadow=\"hover\" class=\"section\">\r\n                        <h2 class=\"section-title\">联系我们 · 招聘启事</h2>\r\n                        <h3 class=\"recruit-title\">兼职远程办公人工智能教育科研教师招募启事</h3>\r\n                        <p class=\"para\">\r\n                            为推动人工智能与学科教育深度融合，探索高效教学新模式，现面向全国公开招募人工智能教育科研教师，具体要求如下：\r\n                        </p>\r\n                        <h4 class=\"sub\">一、招募岗位</h4>\r\n                        <p class=\"para\">\r\n                            人工智能教育科研教师（覆盖小初高各学段，含语文、数学、英语、物理、化学、生物等学科）。\r\n                        </p>\r\n                        <h4 class=\"sub\">二、核心任职要求</h4>\r\n                        <ul class=\"list\">\r\n                            <li>教学经验扎实：需具备不少于 5 年一线教学经验，且拥有不少于 3 年毕业班教学经历，熟悉对应学段学科教学大纲与考试体系，能精准把握教学重难点。</li>\r\n                            <li>教学成果突出：需为现任一线骨干教师，教学成绩在区域或学校内处于一流水平；有学科竞赛指导经验（如数学奥赛、物理竞赛、英语能力竞赛等）者优先，能带领学生在各级赛事中取得优异成绩者可优先考虑。\r\n                            </li>\r\n                            <li>基础技能达标：熟练掌握计算机基础操作（如文档处理、数据统计、多媒体教学工具使用等），能快速适应人工智能教育相关工具的学习与应用。</li>\r\n                        </ul>\r\n                        <h4 class=\"sub\">三、薪资福利</h4>\r\n                        <p class=\"para\">\r\n                            一经聘用，提供行业内极具竞争力的薪酬待遇（含基础工资、绩效奖金、科研补贴、竞赛指导奖励等），同时配套完善的职业发展培训、科研资源支持及晋升通道，具体待遇可面议。\r\n                        </p>\r\n                        <h4 class=\"sub\">四、联系方式</h4>\r\n                        <p class=\"para\">\r\n                            若您符合上述条件，且渴望投身人工智能教育科研事业，欢迎通过以下方式联系我们：\r\n                        </p>\r\n                        <el-alert title=\"简历投递邮箱：<EMAIL>（邮件主题请注明“人工智能教育科研教师 + 学段 + 学科”）\" type=\"info\" show-icon\r\n                            :closable=\"false\" />\r\n                    </el-card>\r\n                </el-col>\r\n            </el-row>\r\n        </div>\r\n        <site-footer />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport SiteFooter from '@/components/SiteFooter'\r\nexport default {\r\n    name: 'AboutPage',\r\n    components: { SiteFooter }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.about-page {\r\n    padding: 20px;\r\n}\r\n\r\n.hero {\r\n    background: linear-gradient(135deg, #f5f7fa 0%, #e6ecf5 100%);\r\n    border: none;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.hero-content {\r\n    max-width: 1100px;\r\n    margin: 0 auto;\r\n    padding: 24px 8px;\r\n    text-align: center;\r\n}\r\n\r\n.title {\r\n    margin: 0;\r\n    font-size: 28px;\r\n    font-weight: 700;\r\n    color: #303133;\r\n}\r\n\r\n.subtitle {\r\n    margin: 6px 0 0;\r\n    color: #909399;\r\n    font-size: 14px;\r\n}\r\n\r\n.content {\r\n    max-width: 1100px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.section {\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.section-title {\r\n    margin: 0 0 8px;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n}\r\n\r\n.recruit-title {\r\n    margin: 8px 0 12px;\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n}\r\n\r\n.sub {\r\n    margin: 12px 0 6px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.para {\r\n    margin: 0 0 8px;\r\n    line-height: 1.8;\r\n    color: #606266;\r\n}\r\n\r\n.list {\r\n    margin: 0 0 8px 18px;\r\n    padding: 0;\r\n}\r\n\r\n.list li {\r\n    line-height: 1.8;\r\n    color: #606266;\r\n}\r\n</style>\r\n"]}]}