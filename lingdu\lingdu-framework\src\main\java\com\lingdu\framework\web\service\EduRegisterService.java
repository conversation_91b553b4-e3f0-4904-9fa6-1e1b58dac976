package com.lingdu.framework.web.service;

import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.lingdu.common.constant.Constants;
import com.lingdu.common.constant.UserConstants;
import com.lingdu.common.core.domain.entity.SysUser;
import com.lingdu.common.core.domain.entity.EduStudentInfo;
import com.lingdu.common.core.domain.model.RegisterBody;
import com.lingdu.common.core.domain.model.StudentRegisterBody;
import com.lingdu.common.core.domain.model.TeacherRegisterBody;
import com.lingdu.common.core.domain.model.ParentRegisterBody;
import com.lingdu.common.core.redis.RedisCache;
import com.lingdu.common.exception.ServiceException;
import com.lingdu.common.utils.DateUtils;
import com.lingdu.common.utils.MessageUtils;
import com.lingdu.common.utils.SecurityUtils;
import com.lingdu.common.utils.StringUtils;
import com.lingdu.framework.manager.AsyncManager;
import com.lingdu.framework.manager.factory.AsyncFactory;
import com.lingdu.system.service.ISysConfigService;
import com.lingdu.system.service.ISysUserService;

/**
 * 教育系统注册服务类，支持学生、家长、教师分角色注册
 * 
 * <AUTHOR>
 */
@Service
public class EduRegisterService extends SysRegisterService
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 学生注册
     * 
     * @param registerBody 学生注册信息
     * @return 结果
     */
    @Transactional
    public String registerStudent(StudentRegisterBody registerBody)
    {
        String msg = validateStudentRegister(registerBody);
        if (StringUtils.isNotEmpty(msg))
        {
            return msg;
        }

        // 验证码校验
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            validateCaptcha(registerBody.getUsername(), registerBody.getCode(), registerBody.getUuid());
        }

        // 创建用户基本信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(registerBody.getUsername());
        sysUser.setNickName(registerBody.getRealName());
        sysUser.setUserType("01"); // 学生类型
        sysUser.setPhonenumber(registerBody.getUsername()); // 使用手机号作为用户名
        sysUser.setPwdUpdateDate(DateUtils.getNowDate());
        sysUser.setPassword(SecurityUtils.encryptPassword(registerBody.getPassword()));
        sysUser.setDeptId(103L); // 默认部门
        
        // 自动分配学生角色
        Long[] roleIds = {100L}; // 学生角色ID
        sysUser.setRoleIds(roleIds);

        // 注册用户
        boolean regFlag = userService.registerUser(sysUser);
        if (!regFlag)
        {
            return "注册失败,请联系系统管理人员";
        }

        // 分配角色
        userService.insertUserAuth(sysUser.getUserId(), roleIds);

        // 创建学生详细信息
        createStudentInfo(sysUser.getUserId(), registerBody);

        // 记录注册日志
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(registerBody.getUsername(), Constants.REGISTER, 
            "学生注册成功"));

        return "";
    }

    /**
     * 教师注册
     * 
     * @param registerBody 教师注册信息
     * @return 结果
     */
    @Transactional
    public String registerTeacher(TeacherRegisterBody registerBody)
    {
        String msg = validateTeacherRegister(registerBody);
        if (StringUtils.isNotEmpty(msg))
        {
            return msg;
        }

        // 验证码校验
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            validateCaptcha(registerBody.getUsername(), registerBody.getCode(), registerBody.getUuid());
        }

        // 创建用户基本信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(registerBody.getUsername());
        sysUser.setNickName(registerBody.getRealName());
        sysUser.setUserType("03"); // 教师类型
        sysUser.setPhonenumber(registerBody.getUsername());
        sysUser.setPwdUpdateDate(DateUtils.getNowDate());
        sysUser.setPassword(SecurityUtils.encryptPassword(registerBody.getPassword()));
        sysUser.setDeptId(103L);
        sysUser.setStatus("1"); // 待审核状态，审核通过后启用
        
        // 自动分配教师角色
        Long[] roleIds = {102L}; // 教师角色ID
        sysUser.setRoleIds(roleIds);

        // 注册用户
        boolean regFlag = userService.registerUser(sysUser);
        if (!regFlag)
        {
            return "注册失败,请联系系统管理人员";
        }

        // 分配角色
        userService.insertUserAuth(sysUser.getUserId(), roleIds);

        // 创建教师认证信息
        createTeacherInfo(sysUser.getUserId(), registerBody);

        // 记录注册日志
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(registerBody.getUsername(), Constants.REGISTER, 
            "教师注册成功，待审核"));

        return "";
    }

    /**
     * 家长注册
     * 
     * @param registerBody 家长注册信息
     * @return 结果
     */
    @Transactional
    public String registerParent(ParentRegisterBody registerBody)
    {
        String msg = validateParentRegister(registerBody);
        if (StringUtils.isNotEmpty(msg))
        {
            return msg;
        }

        // 验证码校验
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            validateCaptcha(registerBody.getUsername(), registerBody.getCode(), registerBody.getUuid());
        }

        // 创建用户基本信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(registerBody.getUsername());
        sysUser.setNickName(registerBody.getRealName());
        sysUser.setUserType("02"); // 家长类型
        sysUser.setPhonenumber(registerBody.getUsername());
        sysUser.setPwdUpdateDate(DateUtils.getNowDate());
        sysUser.setPassword(SecurityUtils.encryptPassword(registerBody.getPassword()));
        sysUser.setDeptId(103L);
        
        // 自动分配家长角色
        Long[] roleIds = {101L}; // 家长角色ID
        sysUser.setRoleIds(roleIds);

        // 注册用户
        boolean regFlag = userService.registerUser(sysUser);
        if (!regFlag)
        {
            return "注册失败,请联系系统管理人员";
        }

        // 分配角色
        userService.insertUserAuth(sysUser.getUserId(), roleIds);

        // 记录注册日志
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(registerBody.getUsername(), Constants.REGISTER, 
            "家长注册成功"));

        return "";
    }

    /**
     * 发送短信验证码
     * 
     * @param phoneNumber 手机号
     * @param verifyType 验证类型
     * @return 结果
     */
    public String sendSmsCode(String phoneNumber, String verifyType)
    {
        // 验证手机号格式
        if (!phoneNumber.matches("^1[3-9]\\d{9}$"))
        {
            return "手机号格式不正确";
        }

        // 检查发送频率限制（1分钟内只能发送一次）
        String rateKey = "sms_rate_limit:" + phoneNumber;
        if (redisCache.hasKey(rateKey))
        {
            return "发送过于频繁，请稍后再试";
        }

        // 生成6位数字验证码
        String verifyCode = String.format("%06d", new Random().nextInt(999999));
        
        // 存储验证码到Redis，有效期5分钟
        String codeKey = "sms_code:" + phoneNumber + ":" + verifyType;
        redisCache.setCacheObject(codeKey, verifyCode, 5, TimeUnit.MINUTES);
        
        // 设置发送频率限制，1分钟
        redisCache.setCacheObject(rateKey, "1", 1, TimeUnit.MINUTES);

        // TODO: 这里应该调用实际的短信发送服务
        // 现在只是模拟发送，实际项目中需要集成阿里云、腾讯云等短信服务
        System.out.println("发送短信验证码到 " + phoneNumber + ": " + verifyCode);

        return "";
    }

    /**
     * 验证短信验证码
     * 
     * @param phoneNumber 手机号
     * @param verifyCode 验证码
     * @param verifyType 验证类型
     * @return 是否验证成功
     */
    public boolean verifySmsCode(String phoneNumber, String verifyCode, String verifyType)
    {
        String codeKey = "sms_code:" + phoneNumber + ":" + verifyType;
        String storedCode = redisCache.getCacheObject(codeKey);
        
        if (StringUtils.isEmpty(storedCode))
        {
            return false;
        }
        
        boolean isValid = storedCode.equals(verifyCode);
        if (isValid)
        {
            // 验证成功后删除验证码
            redisCache.deleteObject(codeKey);
        }
        
        return isValid;
    }

    /**
     * 验证学生注册信息
     */
    private String validateStudentRegister(StudentRegisterBody registerBody)
    {
        String username = registerBody.getUsername();
        String password = registerBody.getPassword();

        if (StringUtils.isEmpty(username))
        {
            return "手机号不能为空";
        }
        else if (!username.matches("^1[3-9]\\d{9}$"))
        {
            return "手机号格式不正确";
        }
        else if (StringUtils.isEmpty(password))
        {
            return "密码不能为空";
        }
        else if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            return "密码长度必须在5到20个字符之间";
        }
        else if (StringUtils.isEmpty(registerBody.getRealName()))
        {
            return "真实姓名不能为空";
        }
        else if (StringUtils.isEmpty(registerBody.getGradeLevel()))
        {
            return "学段不能为空";
        }
        else if (StringUtils.isEmpty(registerBody.getPrimarySubject()))
        {
            return "主学科不能为空";
        }

        // 检查用户名是否已存在
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        if (!userService.checkUserNameUnique(sysUser))
        {
            return "手机号已被注册";
        }

        return "";
    }

    /**
     * 验证教师注册信息
     */
    private String validateTeacherRegister(TeacherRegisterBody registerBody)
    {
        String username = registerBody.getUsername();
        String password = registerBody.getPassword();

        if (StringUtils.isEmpty(username))
        {
            return "手机号不能为空";
        }
        else if (!username.matches("^1[3-9]\\d{9}$"))
        {
            return "手机号格式不正确";
        }
        else if (StringUtils.isEmpty(password))
        {
            return "密码不能为空";
        }
        else if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            return "密码长度必须在5到20个字符之间";
        }
        else if (StringUtils.isEmpty(registerBody.getRealName()))
        {
            return "真实姓名不能为空";
        }
        else if (StringUtils.isEmpty(registerBody.getTeacherCertNumber()))
        {
            return "教师资格证编号不能为空";
        }
        else if (StringUtils.isEmpty(registerBody.getTeachingGradeLevels()))
        {
            return "教学学段不能为空";
        }
        else if (StringUtils.isEmpty(registerBody.getTeachingSubjects()))
        {
            return "教学学科不能为空";
        }

        // 检查用户名是否已存在
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        if (!userService.checkUserNameUnique(sysUser))
        {
            return "手机号已被注册";
        }

        return "";
    }

    /**
     * 验证家长注册信息
     */
    private String validateParentRegister(ParentRegisterBody registerBody)
    {
        String username = registerBody.getUsername();
        String password = registerBody.getPassword();

        if (StringUtils.isEmpty(username))
        {
            return "手机号不能为空";
        }
        else if (!username.matches("^1[3-9]\\d{9}$"))
        {
            return "手机号格式不正确";
        }
        else if (StringUtils.isEmpty(password))
        {
            return "密码不能为空";
        }
        else if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            return "密码长度必须在5到20个字符之间";
        }
        else if (StringUtils.isEmpty(registerBody.getRealName()))
        {
            return "真实姓名不能为空";
        }

        // 检查用户名是否已存在
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        if (!userService.checkUserNameUnique(sysUser))
        {
            return "手机号已被注册";
        }

        return "";
    }

    /**
     * 创建学生详细信息
     */
    private void createStudentInfo(Long userId, StudentRegisterBody registerBody)
    {
        EduStudentInfo studentInfo = new EduStudentInfo();
        studentInfo.setUserId(userId);
        studentInfo.setRealName(registerBody.getRealName());
        studentInfo.setIdCard(registerBody.getIdCard());
        studentInfo.setGradeLevel(registerBody.getGradeLevel());
        studentInfo.setPrimarySubject(registerBody.getPrimarySubject());
        studentInfo.setSecondarySubjects(registerBody.getSecondarySubjects());
        studentInfo.setMemberType(registerBody.getMemberType());
        studentInfo.setSchoolName(registerBody.getSchoolName());
        studentInfo.setClassName(registerBody.getClassName());
        studentInfo.setParentPhone(registerBody.getUsername()); // 暂时使用学生手机号
        studentInfo.setStatus("0");
        studentInfo.setCreateBy("system");
        studentInfo.setCreateTime(new Date());

        // 设置会员到期时间
        if ("free_trial".equals(registerBody.getMemberType()))
        {
            // 免费体验7天
            Date expireDate = DateUtils.addDays(new Date(), 7);
            studentInfo.setMemberExpireDate(expireDate);
        }

        // 生成学号
        String studentNumber = generateStudentNumber(registerBody.getGradeLevel());
        studentInfo.setStudentNumber(studentNumber);

        // TODO: 这里需要调用学生信息服务保存数据
        // studentInfoService.insertStudentInfo(studentInfo);
    }

    /**
     * 创建教师认证信息
     */
    private void createTeacherInfo(Long userId, TeacherRegisterBody registerBody)
    {
        // TODO: 创建教师认证信息
        // EduTeacherInfo teacherInfo = new EduTeacherInfo();
        // 设置相关字段...
        // teacherInfoService.insertTeacherInfo(teacherInfo);
    }

    /**
     * 生成学号
     */
    private String generateStudentNumber(String gradeLevel)
    {
        // 简单的学号生成规则：年份 + 学段代码 + 4位随机数
        String year = String.valueOf(new Date().getYear() + 1900);
        String gradeLevelCode = getGradeLevelCode(gradeLevel);
        String randomNum = String.format("%04d", new Random().nextInt(9999));
        return year + gradeLevelCode + randomNum;
    }

    /**
     * 获取学段代码
     */
    private String getGradeLevelCode(String gradeLevel)
    {
        switch (gradeLevel)
        {
            case "pre_primary": return "00";
            case "primary_1": case "primary_2": case "primary_3":
            case "primary_4": case "primary_5": case "primary_6": return "01";
            case "junior_1": case "junior_2": case "junior_3": return "02";
            case "senior_1": case "senior_2": case "senior_3": return "03";
            default: return "99";
        }
    }
}
