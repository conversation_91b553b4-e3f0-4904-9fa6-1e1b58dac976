package com.lingdu.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lingdu.common.annotation.Excel;
import com.lingdu.common.core.domain.BaseEntity;

/**
 * 学生信息扩展对象 edu_student_info
 * 
 * <AUTHOR>
 */
public class EduStudentInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 学生信息ID */
    private Long studentId;

    /** 关联用户ID */
    @Excel(name = "关联用户ID")
    private Long userId;

    /** 学号 */
    @Excel(name = "学号")
    private String studentNumber;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String realName;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 学段 */
    @Excel(name = "学段")
    private String gradeLevel;

    /** 主学科 */
    @Excel(name = "主学科")
    private String primarySubject;

    /** 副学科，逗号分隔 */
    @Excel(name = "副学科")
    private String secondarySubjects;

    /** 会员类型 */
    @Excel(name = "会员类型")
    private String memberType;

    /** 会员到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "会员到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date memberExpireDate;

    /** 学校名称 */
    @Excel(name = "学校名称")
    private String schoolName;

    /** 班级名称 */
    @Excel(name = "班级名称")
    private String className;

    /** 家长手机号 */
    @Excel(name = "家长手机号")
    private String parentPhone;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setStudentNumber(String studentNumber) 
    {
        this.studentNumber = studentNumber;
    }

    public String getStudentNumber() 
    {
        return studentNumber;
    }

    public void setRealName(String realName) 
    {
        this.realName = realName;
    }

    public String getRealName() 
    {
        return realName;
    }

    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }

    public void setGradeLevel(String gradeLevel) 
    {
        this.gradeLevel = gradeLevel;
    }

    public String getGradeLevel() 
    {
        return gradeLevel;
    }

    public void setPrimarySubject(String primarySubject) 
    {
        this.primarySubject = primarySubject;
    }

    public String getPrimarySubject() 
    {
        return primarySubject;
    }

    public void setSecondarySubjects(String secondarySubjects) 
    {
        this.secondarySubjects = secondarySubjects;
    }

    public String getSecondarySubjects() 
    {
        return secondarySubjects;
    }

    public void setMemberType(String memberType) 
    {
        this.memberType = memberType;
    }

    public String getMemberType() 
    {
        return memberType;
    }

    public void setMemberExpireDate(Date memberExpireDate) 
    {
        this.memberExpireDate = memberExpireDate;
    }

    public Date getMemberExpireDate() 
    {
        return memberExpireDate;
    }

    public void setSchoolName(String schoolName) 
    {
        this.schoolName = schoolName;
    }

    public String getSchoolName() 
    {
        return schoolName;
    }

    public void setClassName(String className) 
    {
        this.className = className;
    }

    public String getClassName() 
    {
        return className;
    }

    public void setParentPhone(String parentPhone) 
    {
        this.parentPhone = parentPhone;
    }

    public String getParentPhone() 
    {
        return parentPhone;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return "EduStudentInfo{" +
                "studentId=" + studentId +
                ", userId=" + userId +
                ", studentNumber='" + studentNumber + '\'' +
                ", realName='" + realName + '\'' +
                ", idCard='" + idCard + '\'' +
                ", gradeLevel='" + gradeLevel + '\'' +
                ", primarySubject='" + primarySubject + '\'' +
                ", secondarySubjects='" + secondarySubjects + '\'' +
                ", memberType='" + memberType + '\'' +
                ", memberExpireDate=" + memberExpireDate +
                ", schoolName='" + schoolName + '\'' +
                ", className='" + className + '\'' +
                ", parentPhone='" + parentPhone + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
