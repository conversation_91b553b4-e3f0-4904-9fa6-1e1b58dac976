package com.lingdu.web.controller.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.lingdu.common.core.controller.BaseController;
import com.lingdu.common.core.domain.AjaxResult;
import com.lingdu.framework.web.service.EduRegisterServiceMinimal;

/**
 * 教育系统注册控制器 - 精简版
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/edu")
public class EduRegisterControllerMinimal extends BaseController
{
    @Autowired
    private EduRegisterServiceMinimal eduRegisterService;

    /**
     * 学生注册
     */
    @PostMapping("/register/student")
    public AjaxResult registerStudent(@RequestBody StudentRegisterRequest request)
    {
        String result = eduRegisterService.registerStudent(
            request.getUsername(),
            request.getPassword(), 
            request.getRealName(),
            request.getGradeLevel(),
            request.getPrimarySubject(),
            request.getMemberType()
        );
        return success(result);
    }

    /**
     * 教师注册
     */
    @PostMapping("/register/teacher")
    public AjaxResult registerTeacher(@RequestBody TeacherRegisterRequest request)
    {
        String result = eduRegisterService.registerTeacher(
            request.getUsername(),
            request.getPassword(),
            request.getRealName(),
            request.getIdCard(),
            request.getTeacherCertNumber(),
            request.getTeachingSubjects()
        );
        return success(result);
    }

    /**
     * 家长注册
     */
    @PostMapping("/register/parent")
    public AjaxResult registerParent(@RequestBody ParentRegisterRequest request)
    {
        String result = eduRegisterService.registerParent(
            request.getUsername(),
            request.getPassword(),
            request.getRealName(),
            request.getPermissionLevel()
        );
        return success(result);
    }

    /**
     * 发送短信验证码
     */
    @PostMapping("/sms/send")
    public AjaxResult sendSmsCode(@RequestParam String phoneNumber, @RequestParam String verifyType)
    {
        eduRegisterService.sendSmsCode(phoneNumber, verifyType);
        return success("验证码发送成功");
    }

    /**
     * 验证短信验证码
     */
    @PostMapping("/sms/verify")
    public AjaxResult verifySmsCode(@RequestParam String phoneNumber, 
                                   @RequestParam String verifyCode, 
                                   @RequestParam String verifyType)
    {
        boolean result = eduRegisterService.verifySmsCode(phoneNumber, verifyCode, verifyType);
        return success(result ? "验证成功" : "验证失败");
    }

    /**
     * 学生注册请求体
     */
    public static class StudentRegisterRequest
    {
        private String username;
        private String password;
        private String realName;
        private String gradeLevel;
        private String primarySubject;
        private String memberType = "free_trial";

        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getGradeLevel() { return gradeLevel; }
        public void setGradeLevel(String gradeLevel) { this.gradeLevel = gradeLevel; }
        public String getPrimarySubject() { return primarySubject; }
        public void setPrimarySubject(String primarySubject) { this.primarySubject = primarySubject; }
        public String getMemberType() { return memberType; }
        public void setMemberType(String memberType) { this.memberType = memberType; }
    }

    /**
     * 教师注册请求体
     */
    public static class TeacherRegisterRequest
    {
        private String username;
        private String password;
        private String realName;
        private String idCard;
        private String teacherCertNumber;
        private String teachingSubjects;

        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getIdCard() { return idCard; }
        public void setIdCard(String idCard) { this.idCard = idCard; }
        public String getTeacherCertNumber() { return teacherCertNumber; }
        public void setTeacherCertNumber(String teacherCertNumber) { this.teacherCertNumber = teacherCertNumber; }
        public String getTeachingSubjects() { return teachingSubjects; }
        public void setTeachingSubjects(String teachingSubjects) { this.teachingSubjects = teachingSubjects; }
    }

    /**
     * 家长注册请求体
     */
    public static class ParentRegisterRequest
    {
        private String username;
        private String password;
        private String realName;
        private String permissionLevel = "view_only";

        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getPermissionLevel() { return permissionLevel; }
        public void setPermissionLevel(String permissionLevel) { this.permissionLevel = permissionLevel; }
    }
}
