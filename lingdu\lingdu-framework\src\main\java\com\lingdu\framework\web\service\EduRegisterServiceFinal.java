package com.lingdu.framework.web.service;

import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.lingdu.common.constant.Constants;
import com.lingdu.common.constant.UserConstants;
import com.lingdu.common.core.domain.entity.SysUser;
import com.lingdu.common.core.redis.RedisCache;
import com.lingdu.common.exception.ServiceException;
import com.lingdu.common.utils.DateUtils;
import com.lingdu.common.utils.SecurityUtils;
import com.lingdu.common.utils.StringUtils;
import com.lingdu.system.service.ISysUserService;

/**
 * 教育系统注册服务 - 基于现有表结构
 * 
 * <AUTHOR>
 */
@Service
public class EduRegisterServiceFinal
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 学生注册
     */
    @Transactional
    public String registerStudent(String username, String password, String realName, 
                                String grade, String stage, String mainSubject, 
                                String extraSubjects, String membershipType)
    {
        // 验证用户名是否已存在
        if (!userService.checkUserNameUnique(username))
        {
            throw new ServiceException("注册失败，手机号已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(username);
        user.setNickName(realName);
        user.setUserType("01"); // 学生
        user.setPhonenumber(username);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setStatus("0");
        user.setCreateBy("register");
        user.setCreateTime(DateUtils.getNowDate());
        user.setRemark("学生注册");

        boolean regFlag = userService.registerUser(user);
        if (!regFlag)
        {
            throw new ServiceException("注册失败，请联系系统管理人员");
        }

        // TODO: 创建学生信息记录
        // INSERT INTO student_info (user_id, grade, stage, main_subject, extra_subjects, membership_type)
        // VALUES (user.getUserId(), grade, stage, mainSubject, extraSubjects, membershipType)
        
        return "学生注册成功";
    }

    /**
     * 教师注册
     */
    @Transactional
    public String registerTeacher(String username, String password, String realName,
                                String certificateNo, String stages, String subjects)
    {
        // 验证用户名是否已存在
        if (!userService.checkUserNameUnique(username))
        {
            throw new ServiceException("注册失败，手机号已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(username);
        user.setNickName(realName);
        user.setUserType("03"); // 教师
        user.setPhonenumber(username);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setStatus("1"); // 待审核状态
        user.setCreateBy("register");
        user.setCreateTime(DateUtils.getNowDate());
        user.setRemark("教师注册，待审核");

        boolean regFlag = userService.registerUser(user);
        if (!regFlag)
        {
            throw new ServiceException("注册失败，请联系系统管理人员");
        }

        // TODO: 创建教师信息记录
        // INSERT INTO teacher_info (user_id, phone, name, certificate_no, verify_status, stages, subjects)
        // VALUES (user.getUserId(), username, realName, certificateNo, 0, stages, subjects)
        
        return "教师注册成功，请等待管理员审核";
    }

    /**
     * 家长注册
     */
    @Transactional
    public String registerParent(String username, String password, String realName)
    {
        // 验证用户名是否已存在
        if (!userService.checkUserNameUnique(username))
        {
            throw new ServiceException("注册失败，手机号已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(username);
        user.setNickName(realName);
        user.setUserType("02"); // 家长
        user.setPhonenumber(username);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setStatus("0");
        user.setCreateBy("register");
        user.setCreateTime(DateUtils.getNowDate());
        user.setRemark("家长注册");

        boolean regFlag = userService.registerUser(user);
        if (!regFlag)
        {
            throw new ServiceException("注册失败，请联系系统管理人员");
        }

        // TODO: 创建家长信息记录
        // INSERT INTO parent_info (user_id, phone, name)
        // VALUES (user.getUserId(), username, realName)
        
        return "家长注册成功，请登录后关联您的孩子";
    }

    /**
     * 家长关联学生
     */
    @Transactional
    public String bindStudent(Long parentUserId, String studentPhone, String studentName, String relation)
    {
        // 查找学生用户
        SysUser studentUser = userService.selectUserByUserName(studentPhone);
        if (studentUser == null || !"01".equals(studentUser.getUserType()))
        {
            throw new ServiceException("学生不存在或用户类型错误");
        }

        // TODO: 验证学生姓名是否匹配
        // 从student_info表查询学生信息进行验证

        // TODO: 创建关联关系
        // INSERT INTO user_parent_student (parent_id, student_id, relation)
        // VALUES (parentUserId, studentUser.getUserId(), relation)
        
        return "关联学生成功";
    }

    /**
     * 发送短信验证码
     */
    public void sendSmsCode(String phoneNumber, String verifyType)
    {
        // 检查发送频率限制
        String rateKey = "sms_rate:" + phoneNumber;
        if (redisCache.hasKey(rateKey))
        {
            throw new ServiceException("发送过于频繁，请稍后再试");
        }

        // 生成6位验证码
        String verifyCode = String.format("%06d", new Random().nextInt(999999));
        
        // 存储验证码到Redis，5分钟过期
        String cacheKey = "sms_code:" + phoneNumber + ":" + verifyType;
        redisCache.setCacheObject(cacheKey, verifyCode, 5, TimeUnit.MINUTES);
        
        // 设置发送频率限制，60秒内不能重复发送
        redisCache.setCacheObject(rateKey, "1", 60, TimeUnit.SECONDS);
        
        // TODO: 调用短信服务发送验证码
        // 同时可以存储到sms_verify_code表作为备份
        System.out.println("发送验证码到 " + phoneNumber + ": " + verifyCode);
    }

    /**
     * 验证短信验证码
     */
    public boolean verifySmsCode(String phoneNumber, String verifyCode, String verifyType)
    {
        String cacheKey = "sms_code:" + phoneNumber + ":" + verifyType;
        String cachedCode = redisCache.getCacheObject(cacheKey);
        
        if (StringUtils.isEmpty(cachedCode))
        {
            throw new ServiceException("验证码已过期或不存在");
        }
        
        if (!verifyCode.equals(cachedCode))
        {
            throw new ServiceException("验证码错误");
        }
        
        // 验证成功后删除验证码
        redisCache.deleteObject(cacheKey);
        return true;
    }

    /**
     * 短信验证码登录
     */
    public SysUser loginBySms(String phoneNumber, String verifyCode, String userType)
    {
        // 验证短信验证码
        if (!verifySmsCode(phoneNumber, verifyCode, "login"))
        {
            throw new ServiceException("验证码验证失败");
        }
        
        // 查找用户
        SysUser user = userService.selectUserByUserName(phoneNumber);
        if (user == null)
        {
            throw new ServiceException("用户不存在");
        }
        
        // 验证用户类型
        if (!userType.equals(user.getUserType()))
        {
            throw new ServiceException("用户类型不匹配");
        }
        
        // 检查用户状态
        if (UserConstants.USER_DISABLE.equals(user.getStatus()))
        {
            throw new ServiceException("用户已被停用，请联系管理员");
        }
        
        return user;
    }

    /**
     * 获取字典数据接口（供前端下拉框使用）
     */
    public String getDictData(String dictType)
    {
        // TODO: 调用字典服务获取数据
        // 返回对应字典类型的数据列表
        return "字典数据";
    }
}
