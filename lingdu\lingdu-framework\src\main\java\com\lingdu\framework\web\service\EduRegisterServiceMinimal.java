package com.lingdu.framework.web.service;

import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.lingdu.common.constant.Constants;
import com.lingdu.common.constant.UserConstants;
import com.lingdu.common.core.domain.entity.SysUser;
import com.lingdu.common.core.redis.RedisCache;
import com.lingdu.common.exception.ServiceException;
import com.lingdu.common.utils.DateUtils;
import com.lingdu.common.utils.MessageUtils;
import com.lingdu.common.utils.SecurityUtils;
import com.lingdu.common.utils.StringUtils;
import com.lingdu.system.service.ISysUserService;

/**
 * 教育系统注册服务 - 精简版
 * 
 * <AUTHOR>
 */
@Service
public class EduRegisterServiceMinimal
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 学生注册
     */
    @Transactional
    public String registerStudent(String username, String password, String realName, 
                                String gradeLevel, String primarySubject, String memberType)
    {
        // 验证用户名是否已存在
        if (!userService.checkUserNameUnique(username))
        {
            throw new ServiceException("注册失败，手机号已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(username);
        user.setNickName(realName);
        user.setUserType("01"); // 学生
        user.setPhonenumber(username);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setStatus("0");
        user.setCreateBy("register");
        user.setCreateTime(DateUtils.getNowDate());
        user.setRemark("学生注册");

        boolean regFlag = userService.registerUser(user);
        if (!regFlag)
        {
            throw new ServiceException("注册失败，请联系系统管理人员");
        }

        // TODO: 创建学生信息记录
        // 这里需要创建对应的学生信息表记录，包含学段、学科、会员类型等信息
        
        return "注册成功";
    }

    /**
     * 教师注册
     */
    @Transactional
    public String registerTeacher(String username, String password, String realName,
                                String idCard, String teacherCertNumber, String teachingSubjects)
    {
        // 验证用户名是否已存在
        if (!userService.checkUserNameUnique(username))
        {
            throw new ServiceException("注册失败，手机号已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(username);
        user.setNickName(realName);
        user.setUserType("03"); // 教师
        user.setPhonenumber(username);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setStatus("1"); // 待审核状态
        user.setCreateBy("register");
        user.setCreateTime(DateUtils.getNowDate());
        user.setRemark("教师注册，待审核");

        boolean regFlag = userService.registerUser(user);
        if (!regFlag)
        {
            throw new ServiceException("注册失败，请联系系统管理人员");
        }

        // TODO: 创建教师信息记录
        // 这里需要创建对应的教师信息表记录，包含身份证、资格证、教学学科等信息
        
        return "注册成功，请等待管理员审核";
    }

    /**
     * 家长注册
     */
    @Transactional
    public String registerParent(String username, String password, String realName,
                               String permissionLevel)
    {
        // 验证用户名是否已存在
        if (!userService.checkUserNameUnique(username))
        {
            throw new ServiceException("注册失败，手机号已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUserName(username);
        user.setNickName(realName);
        user.setUserType("02"); // 家长
        user.setPhonenumber(username);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setStatus("0");
        user.setCreateBy("register");
        user.setCreateTime(DateUtils.getNowDate());
        user.setRemark("家长注册");

        boolean regFlag = userService.registerUser(user);
        if (!regFlag)
        {
            throw new ServiceException("注册失败，请联系系统管理人员");
        }

        // TODO: 记录家长权限级别
        
        return "注册成功，请登录后关联您的孩子";
    }

    /**
     * 发送短信验证码
     */
    public void sendSmsCode(String phoneNumber, String verifyType)
    {
        // 检查发送频率限制
        String rateKey = "sms_rate:" + phoneNumber;
        if (redisCache.hasKey(rateKey))
        {
            throw new ServiceException("发送过于频繁，请稍后再试");
        }

        // 生成6位验证码
        String verifyCode = String.format("%06d", new Random().nextInt(999999));
        
        // 存储验证码到Redis，5分钟过期
        String cacheKey = "sms_code:" + phoneNumber + ":" + verifyType;
        redisCache.setCacheObject(cacheKey, verifyCode, 5, TimeUnit.MINUTES);
        
        // 设置发送频率限制，60秒内不能重复发送
        redisCache.setCacheObject(rateKey, "1", 60, TimeUnit.SECONDS);
        
        // TODO: 调用短信服务发送验证码
        // 这里需要集成实际的短信服务提供商
        System.out.println("发送验证码到 " + phoneNumber + ": " + verifyCode);
    }

    /**
     * 验证短信验证码
     */
    public boolean verifySmsCode(String phoneNumber, String verifyCode, String verifyType)
    {
        String cacheKey = "sms_code:" + phoneNumber + ":" + verifyType;
        String cachedCode = redisCache.getCacheObject(cacheKey);
        
        if (StringUtils.isEmpty(cachedCode))
        {
            throw new ServiceException("验证码已过期或不存在");
        }
        
        if (!verifyCode.equals(cachedCode))
        {
            throw new ServiceException("验证码错误");
        }
        
        // 验证成功后删除验证码
        redisCache.deleteObject(cacheKey);
        return true;
    }

    /**
     * 短信验证码登录
     */
    public SysUser loginBySms(String phoneNumber, String verifyCode, String userType)
    {
        // 验证短信验证码
        if (!verifySmsCode(phoneNumber, verifyCode, "login"))
        {
            throw new ServiceException("验证码验证失败");
        }
        
        // 查找用户
        SysUser user = userService.selectUserByUserName(phoneNumber);
        if (user == null)
        {
            throw new ServiceException("用户不存在");
        }
        
        // 验证用户类型
        if (!userType.equals(user.getUserType()))
        {
            throw new ServiceException("用户类型不匹配");
        }
        
        // 检查用户状态
        if (UserConstants.USER_DISABLE.equals(user.getStatus()))
        {
            throw new ServiceException("用户已被停用，请联系管理员");
        }
        
        return user;
    }

    /**
     * 生成学号
     */
    private String generateStudentNumber()
    {
        // 格式：年份 + 月日 + 4位随机数
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        String randomStr = String.format("%04d", new Random().nextInt(9999));
        return dateStr + randomStr;
    }
}
