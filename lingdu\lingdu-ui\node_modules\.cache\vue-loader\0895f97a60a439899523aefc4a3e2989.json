{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\help\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\help\\index.vue", "mtime": 1758178462824}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgU2l0ZUZvb3RlciBmcm9tICdAL2NvbXBvbmVudHMvU2l0ZUZvb3RlcicNCmV4cG9ydCBkZWZhdWx0IHsNCiAgICBuYW1lOiAnSGVscFBhZ2UnLA0KICAgIGNvbXBvbmVudHM6IHsgU2l0ZUZvb3RlciB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/help", "sourcesContent": ["<template>\r\n    <div class=\"help-page\">\r\n        <el-card class=\"hero\" shadow=\"never\">\r\n            <h1 class=\"title\">帮助中心</h1>\r\n            <p class=\"subtitle\">常见问题与使用指南正在完善中。</p>\r\n        </el-card>\r\n        <site-footer />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport SiteFooter from '@/components/SiteFooter'\r\nexport default {\r\n    name: 'HelpPage',\r\n    components: { SiteFooter }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.help-page {\r\n    padding: 20px;\r\n}\r\n\r\n.hero {\r\n    margin: 0 auto;\r\n    max-width: 1100px;\r\n    border: none;\r\n    background: #f5f7fa;\r\n}\r\n\r\n.title {\r\n    margin: 0;\r\n    padding: 16px 8px 4px;\r\n    font-size: 22px;\r\n    font-weight: 700;\r\n    text-align: center;\r\n}\r\n\r\n.subtitle {\r\n    margin: 0 0 16px;\r\n    color: #909399;\r\n    text-align: center;\r\n}\r\n</style>\r\n"]}]}