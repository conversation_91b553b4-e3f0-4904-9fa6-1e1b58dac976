package com.lingdu.common.core.domain.model;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 教师注册请求体
 * 
 * <AUTHOR>
 */
public class TeacherRegisterBody extends RegisterBody
{
    /** 真实姓名 */
    @NotBlank(message = "真实姓名不能为空")
    @Size(min = 2, max = 30, message = "真实姓名长度必须在2到30个字符之间")
    private String realName;

    /** 身份证号 */
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;

    /** 教师资格证编号 */
    @NotBlank(message = "教师资格证编号不能为空")
    private String teacherCertNumber;

    /** 教师资格证照片（base64编码） */
    private String teacherCertImage;

    /** 教学学段，逗号分隔 */
    @NotBlank(message = "教学学段不能为空")
    private String teachingGradeLevels;

    /** 教学学科，逗号分隔 */
    @NotBlank(message = "教学学科不能为空")
    private String teachingSubjects;

    /** 任职学校 */
    private String schoolName;

    public String getRealName()
    {
        return realName;
    }

    public void setRealName(String realName)
    {
        this.realName = realName;
    }

    public String getIdCard()
    {
        return idCard;
    }

    public void setIdCard(String idCard)
    {
        this.idCard = idCard;
    }

    public String getTeacherCertNumber()
    {
        return teacherCertNumber;
    }

    public void setTeacherCertNumber(String teacherCertNumber)
    {
        this.teacherCertNumber = teacherCertNumber;
    }

    public String getTeacherCertImage()
    {
        return teacherCertImage;
    }

    public void setTeacherCertImage(String teacherCertImage)
    {
        this.teacherCertImage = teacherCertImage;
    }

    public String getTeachingGradeLevels()
    {
        return teachingGradeLevels;
    }

    public void setTeachingGradeLevels(String teachingGradeLevels)
    {
        this.teachingGradeLevels = teachingGradeLevels;
    }

    public String getTeachingSubjects()
    {
        return teachingSubjects;
    }

    public void setTeachingSubjects(String teachingSubjects)
    {
        this.teachingSubjects = teachingSubjects;
    }

    public String getSchoolName()
    {
        return schoolName;
    }

    public void setSchoolName(String schoolName)
    {
        this.schoolName = schoolName;
    }
}
