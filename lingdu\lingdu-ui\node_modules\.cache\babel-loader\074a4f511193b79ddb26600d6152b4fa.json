{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\layout\\components\\Navbar.vue", "mtime": 1758178462816}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1757925919067}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuex", "require", "_Breadcrumb", "_interopRequireDefault", "_TopNav", "_<PERSON>er", "_Screenfull", "_SizeSelect", "_HeaderSearch", "_Git", "_Doc", "_auth", "emits", "components", "Breadcrumb", "TopNav", "<PERSON><PERSON>", "Screenfull", "SizeSelect", "Search", "RuoYiGit", "RuoYiDoc", "data", "activeIndex", "loginLoading", "registerLoading", "computed", "_objectSpread2", "default", "mapGetters", "setting", "get", "$store", "state", "settings", "showSettings", "topNav", "isLoggedIn", "getToken", "methods", "toggleSideBar", "dispatch", "setLayout", "event", "$emit", "handleSelect", "key", "$router", "push", "hasHelp", "options", "routes", "some", "r", "path", "$message", "handleLogin", "_this$$store$getters$", "_this", "hasToken", "getters", "token", "roles", "length", "goLogin", "replace", "finally", "handleRegister", "_this2", "setTimeout", "logout", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "location", "href", "catch", "goProfile"], "sources": ["src/layout/components/Navbar.vue"], "sourcesContent": ["<template>\r\n  <!-- 头部导航 -->\r\n  <el-header class=\"header\" height=\"64px\">\r\n    <el-row type=\"flex\" justify=\"space-between\" align=\"middle\" class=\"header-row\">\r\n      <!-- Logo和标题 -->\r\n      <el-col :span=\"6\">\r\n        <div class=\"logo-section\">\r\n          <div class=\"logo\">\r\n            <i class=\"el-icon-cpu\"></i>\r\n          </div>\r\n          <h1 class=\"title\">灵渡AI学习助手</h1>\r\n        </div>\r\n      </el-col>\r\n\r\n      <!-- 导航链接 -->\r\n      <el-col :span=\"12\" class=\"nav-col\">\r\n        <el-menu mode=\"horizontal\" :default-active=\"activeIndex\" class=\"nav-menu\" @select=\"handleSelect\">\r\n          <el-menu-item index=\"home\">首页</el-menu-item>\r\n          <el-menu-item index=\"about\">关于我们</el-menu-item>\r\n          <el-menu-item index=\"help\">帮助</el-menu-item>\r\n        </el-menu>\r\n      </el-col>\r\n\r\n      <!-- 登录注册 / 退出 -->\r\n      <el-col :span=\"6\" class=\"auth-col\">\r\n        <div class=\"auth-buttons\">\r\n          <el-button v-if=\"isLoggedIn\" @click=\"goProfile\">\r\n            个人中心\r\n          </el-button>\r\n          <el-button v-if=\"!isLoggedIn\" class=\"login-btn\" @click=\"handleLogin\" :loading=\"loginLoading\">\r\n            登录\r\n          </el-button>\r\n          <el-button v-if=\"!isLoggedIn\" type=\"primary\" @click=\"handleRegister\" :loading=\"registerLoading\">\r\n            注册\r\n          </el-button>\r\n          <el-button v-else type=\"danger\" @click=\"logout\">\r\n            退出\r\n          </el-button>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </el-header>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Breadcrumb from '@/components/Breadcrumb'\r\nimport TopNav from '@/components/TopNav'\r\nimport Hamburger from '@/components/Hamburger'\r\nimport Screenfull from '@/components/Screenfull'\r\nimport SizeSelect from '@/components/SizeSelect'\r\nimport Search from '@/components/HeaderSearch'\r\nimport RuoYiGit from '@/components/RuoYi/Git'\r\nimport RuoYiDoc from '@/components/RuoYi/Doc'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  emits: ['setLayout'],\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    RuoYiGit,\r\n    RuoYiDoc\r\n  },\r\n  data() {\r\n    return {\r\n      activeIndex: 'home',\r\n      loginLoading: false,\r\n      registerLoading: false,\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebar',\r\n      'avatar',\r\n      'device',\r\n      'nickName'\r\n    ]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      }\r\n    },\r\n    isLoggedIn() {\r\n      return !!getToken()\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.$store.dispatch('app/toggleSideBar')\r\n    },\r\n    setLayout(event) {\r\n      this.$emit('setLayout')\r\n    },\r\n    handleSelect(key) {\r\n      this.activeIndex = key\r\n      switch (key) {\r\n        case 'home':\r\n          this.$router.push('/')\r\n          break\r\n        case 'about':\r\n          this.$router.push('/about')\r\n          break\r\n        case 'help':\r\n          // 如果存在帮助路由则跳转，否则给出提示\r\n          const hasHelp = this.$router.options.routes.some(r => r.path === '/help')\r\n          if (hasHelp) {\r\n            this.$router.push('/help')\r\n          } else {\r\n            this.$message('帮助页面开发中...')\r\n          }\r\n          break\r\n      }\r\n    },\r\n    handleLogin() {\r\n      this.loginLoading = true\r\n      const hasToken = this.$store.getters.token || this.$store.getters.roles?.length > 0\r\n      const goLogin = () => {\r\n        this.loginLoading = false\r\n        this.$router.replace('/login')\r\n      }\r\n      if (hasToken) {\r\n        // 清除登录态再进入登录页，避免被路由守卫重定向回首页\r\n        this.$store.dispatch('LogOut').finally(goLogin)\r\n      } else {\r\n        goLogin()\r\n      }\r\n    },\r\n    handleRegister() {\r\n      this.registerLoading = true\r\n      setTimeout(() => {\r\n        this.registerLoading = false\r\n        this.$router.push('/register')\r\n      }, 1000)\r\n    },\r\n    logout() {\r\n      this.$confirm('确定退出系统吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$store.dispatch('LogOut').then(() => {\r\n          location.href = '/index'\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    goProfile() {\r\n      this.$router.push('/user/profile')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.navbar {\r\n  height: 50px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);\r\n\r\n  .hamburger-container {\r\n    line-height: 46px;\r\n    height: 100%;\r\n    float: left;\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n    -webkit-tap-highlight-color: transparent;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .025)\r\n    }\r\n  }\r\n\r\n  .breadcrumb-container {\r\n    float: left;\r\n  }\r\n\r\n  .topmenu-container {\r\n    position: absolute;\r\n    left: 50px;\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .right-menu {\r\n    float: right;\r\n    height: 100%;\r\n    line-height: 50px;\r\n\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background .3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, .025)\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      margin-right: 0px;\r\n      padding-right: 0px;\r\n\r\n      .avatar-wrapper {\r\n        margin-top: 10px;\r\n        right: 8px;\r\n        position: relative;\r\n\r\n        .user-avatar {\r\n          cursor: pointer;\r\n          width: 30px;\r\n          height: 30px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .user-nickname {\r\n          position: relative;\r\n          bottom: 10px;\r\n          left: 2px;\r\n          font-size: 14px;\r\n          font-weight: bold;\r\n        }\r\n\r\n        .el-icon-caret-bottom {\r\n          cursor: pointer;\r\n          position: absolute;\r\n          right: -20px;\r\n          top: 25px;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n/* Header navbar styles */\r\n.header {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 1000;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.header-row {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n.logo-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.logo {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 20px;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin: 0;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.nav-col {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.nav-menu {\r\n  border-bottom: none;\r\n  background: transparent;\r\n}\r\n\r\n.nav-menu .el-menu-item {\r\n  color: #606266;\r\n  font-weight: 500;\r\n  border-bottom: 2px solid transparent;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-menu .el-menu-item:hover,\r\n.nav-menu .el-menu-item.is-active {\r\n  color: #409EFF;\r\n  border-bottom-color: #409EFF;\r\n  background: transparent;\r\n}\r\n\r\n.auth-col {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.auth-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.login-btn {\r\n  border: 1px solid #409EFF;\r\n  color: #409EFF;\r\n  background: transparent;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.login-btn:hover {\r\n  background-color: #409EFF;\r\n  color: white;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA6CA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,UAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,WAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,aAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,IAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,IAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,KAAA,GAAAV,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAW,KAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,qBAAA;IACAC,QAAA,EAAAA,YAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,YAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA,GACA,WACA,UACA,UACA,WACA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA;MACA;IACA;IACAC,MAAA;MACAL,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAE,MAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,aAAAC,cAAA;IACA;EAAA,EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAR,MAAA,CAAAS,QAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MACA,KAAAC,KAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAvB,WAAA,GAAAuB,GAAA;MACA,QAAAA,GAAA;QACA;UACA,KAAAC,OAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA;UACA,IAAAC,OAAA,QAAAF,OAAA,CAAAG,OAAA,CAAAC,MAAA,CAAAC,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,IAAA;UAAA;UACA,IAAAL,OAAA;YACA,KAAAF,OAAA,CAAAC,IAAA;UACA;YACA,KAAAO,QAAA;UACA;UACA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,qBAAA;QAAAC,KAAA;MACA,KAAAlC,YAAA;MACA,IAAAmC,QAAA,QAAA3B,MAAA,CAAA4B,OAAA,CAAAC,KAAA,MAAAJ,qBAAA,QAAAzB,MAAA,CAAA4B,OAAA,CAAAE,KAAA,cAAAL,qBAAA,uBAAAA,qBAAA,CAAAM,MAAA;MACA,IAAAC,OAAA,YAAAA,QAAA;QACAN,KAAA,CAAAlC,YAAA;QACAkC,KAAA,CAAAX,OAAA,CAAAkB,OAAA;MACA;MACA,IAAAN,QAAA;QACA;QACA,KAAA3B,MAAA,CAAAS,QAAA,WAAAyB,OAAA,CAAAF,OAAA;MACA;QACAA,OAAA;MACA;IACA;IACAG,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA3C,eAAA;MACA4C,UAAA;QACAD,MAAA,CAAA3C,eAAA;QACA2C,MAAA,CAAArB,OAAA,CAAAC,IAAA;MACA;IACA;IACAsB,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAL,MAAA,CAAAvC,MAAA,CAAAS,QAAA,WAAAmC,IAAA;UACAC,QAAA,CAAAC,IAAA;QACA;MACA,GAAAC,KAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAjC,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}