{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-login.vue?vue&type=style&index=0&id=625b2e30&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-login.vue", "mtime": 1758185473790}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757926224263}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757926225750}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757926224811}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757926223774}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5lZHUtbG9naW4gewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBoZWlnaHQ6IDEwMHZoOwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7Cn0KCi5sb2dpbi1jb250YWluZXIgewogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKICBib3gtc2hhZG93OiAwIDE1cHggMzVweCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgd2lkdGg6IDQ1MHB4OwogIHBhZGRpbmc6IDA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLnJvbGUtdGFicyB7CiAgLmVsLXRhYnNfX2hlYWRlciB7CiAgICBtYXJnaW46IDA7CiAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIH0KCiAgLmVsLXRhYnNfX25hdi13cmFwIHsKICAgIHBhZGRpbmc6IDA7CiAgfQoKICAuZWwtdGFic19faXRlbSB7CiAgICBoZWlnaHQ6IDYwcHg7CiAgICBsaW5lLWhlaWdodDogNjBweDsKICAgIGZvbnQtc2l6ZTogMTZweDsKICAgIGZvbnQtd2VpZ2h0OiA1MDA7CgogICAgJi5pcy1hY3RpdmUgewogICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICAgIH0KICB9CgogIC5yb2xlLWljb24gewogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgcGFkZGluZzogMTBweCAwOwoKICAgIGkgewogICAgICBmb250LXNpemU6IDI0cHg7CiAgICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgfQogIH0KCiAgLmVsLXRhYnNfX2l0ZW0uaXMtYWN0aXZlIC5yb2xlLWljb24gaSB7CiAgICBjb2xvcjogIzQwOUVGRjsKICB9Cn0KCi5sb2dpbi1mb3JtIHsKICBwYWRkaW5nOiAzMHB4IDQwcHggNDBweDsKCiAgLnRpdGxlIHsKICAgIG1hcmdpbjogMCAwIDMwcHggMDsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIGNvbG9yOiAjMzAzMTMzOwogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICBmb250LXNpemU6IDIycHg7CiAgfQoKICAubG9naW4tdHlwZS1zd2l0Y2ggewogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgbWFyZ2luLWJvdHRvbTogMjVweDsKICB9CgogIC5lbC1pbnB1dCB7CiAgICBoZWlnaHQ6IDQ1cHg7CiAgICBpbnB1dCB7CiAgICAgIGhlaWdodDogNDVweDsKICAgICAgYm9yZGVyLXJhZGl1czogNnB4OwogICAgfQogIH0KCiAgLmlucHV0LWljb24gewogICAgaGVpZ2h0OiAzOXB4OwogICAgd2lkdGg6IDE0cHg7CiAgICBtYXJnaW4tbGVmdDogMnB4OwogIH0KCiAgLnNtcy1pbnB1dC1ncm91cCB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICB9CgogIC5sb2dpbi1jb2RlIHsKICAgIHdpZHRoOiAzNSU7CiAgICBoZWlnaHQ6IDQ1cHg7CiAgICBmbG9hdDogcmlnaHQ7CiAgICBpbWcgewogICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7CiAgICAgIGhlaWdodDogNDVweDsKICAgICAgYm9yZGVyLXJhZGl1czogNnB4OwogICAgfQogIH0KCiAgLnJlZ2lzdGVyLWxpbmtzIHsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIG1hcmdpbi10b3A6IDIwcHg7CgogICAgLnNlcGFyYXRvciB7CiAgICAgIG1hcmdpbjogMCAxMHB4OwogICAgICBjb2xvcjogI0RDREZFNjsKICAgIH0KCiAgICAubGluay10eXBlIHsKICAgICAgY29sb3I6ICM0MDlFRkY7CiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTsKCiAgICAgICY6aG92ZXIgewogICAgICAgIGNvbG9yOiAjNjZiMWZmOwogICAgICB9CiAgICB9CiAgfQp9Cgoucm9sZS1ndWlkZSB7CiAgLmd1aWRlLWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICBoNCB7CiAgICAgIGNvbG9yOiAjMzAzMTMzOwogICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwoKICAgICAgaSB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICAgICAgY29sb3I6ICM0MDlFRkY7CiAgICAgIH0KICAgIH0KCiAgICBwIHsKICAgICAgbWFyZ2luOiA1cHggMDsKICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICAgIHBhZGRpbmctbGVmdDogMjRweDsKICAgIH0KICB9Cn0KCi5lbC1sb2dpbi1mb290ZXIgewogIGhlaWdodDogNDBweDsKICBsaW5lLWhlaWdodDogNDBweDsKICBwb3NpdGlvbjogZml4ZWQ7CiAgYm90dG9tOiAwOwogIHdpZHRoOiAxMDAlOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogI2ZmZjsKICBmb250LWZhbWlseTogQXJpYWw7CiAgZm9udC1zaXplOiAxMnB4OwogIGxldHRlci1zcGFjaW5nOiAxcHg7Cn0K"}, {"version": 3, "sources": ["edu-login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0YA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "edu-login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"edu-login\">\n    <div class=\"login-container\">\n      <!-- 角色选择标签页 -->\n      <el-tabs v-model=\"activeRole\" class=\"role-tabs\" @tab-click=\"handleRoleChange\">\n        <el-tab-pane label=\"学生登录\" name=\"student\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-user-solid\"></i>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"家长登录\" name=\"parent\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-user\"></i>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"教师登录\" name=\"teacher\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-s-custom\"></i>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <!-- 登录表单 -->\n      <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\n        <h3 class=\"title\">{{ getRoleTitle() }}</h3>\n        \n        <!-- 登录方式切换 -->\n        <div class=\"login-type-switch\">\n          <el-radio-group v-model=\"loginType\" size=\"small\">\n            <el-radio-button label=\"password\">密码登录</el-radio-button>\n            <el-radio-button label=\"sms\">验证码登录</el-radio-button>\n          </el-radio-group>\n        </div>\n\n        <!-- 手机号输入 -->\n        <el-form-item prop=\"phoneNumber\">\n          <el-input\n            v-model=\"loginForm.phoneNumber\"\n            type=\"text\"\n            auto-complete=\"off\"\n            placeholder=\"请输入手机号\"\n            maxlength=\"11\"\n          >\n            <svg-icon slot=\"prefix\" icon-class=\"phone\" class=\"el-input__icon input-icon\" />\n          </el-input>\n        </el-form-item>\n\n        <!-- 密码输入（密码登录时显示） -->\n        <el-form-item prop=\"password\" v-if=\"loginType === 'password'\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            auto-complete=\"off\"\n            placeholder=\"请输入密码\"\n            @keyup.enter.native=\"handleLogin\"\n          >\n            <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n          </el-input>\n        </el-form-item>\n\n        <!-- 短信验证码输入（验证码登录时显示） -->\n        <el-form-item prop=\"smsCode\" v-if=\"loginType === 'sms'\">\n          <div class=\"sms-input-group\">\n            <el-input\n              v-model=\"loginForm.smsCode\"\n              auto-complete=\"off\"\n              placeholder=\"请输入验证码\"\n              maxlength=\"6\"\n              style=\"width: 60%\"\n              @keyup.enter.native=\"handleLogin\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n            </el-input>\n            <el-button \n              type=\"primary\" \n              :disabled=\"smsCountdown > 0\" \n              @click=\"sendSmsCode\"\n              style=\"width: 38%; margin-left: 2%\"\n            >\n              {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '获取验证码' }}\n            </el-button>\n          </div>\n        </el-form-item>\n\n        <!-- 图形验证码（密码登录时显示） -->\n        <el-form-item prop=\"code\" v-if=\"loginType === 'password' && captchaEnabled\">\n          <el-input\n            v-model=\"loginForm.code\"\n            auto-complete=\"off\"\n            placeholder=\"验证码\"\n            style=\"width: 63%\"\n            @keyup.enter.native=\"handleLogin\"\n          >\n            <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n          </el-input>\n          <div class=\"login-code\">\n            <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"/>\n          </div>\n        </el-form-item>\n\n        <!-- 记住密码 -->\n        <el-checkbox v-model=\"loginForm.rememberMe\" v-if=\"loginType === 'password'\" style=\"margin:0px 0px 25px 0px;\">\n          记住密码\n        </el-checkbox>\n\n        <!-- 登录按钮 -->\n        <el-form-item style=\"width:100%;\">\n          <el-button\n            :loading=\"loading\"\n            size=\"medium\"\n            type=\"primary\"\n            style=\"width:100%;\"\n            @click.native.prevent=\"handleLogin\"\n          >\n            <span v-if=\"!loading\">登 录</span>\n            <span v-else>登 录 中...</span>\n          </el-button>\n        </el-form-item>\n\n        <!-- 注册链接 -->\n        <div class=\"register-links\">\n          <router-link class=\"link-type\" :to=\"getRegisterRoute()\">\n            {{ getRegisterText() }}\n          </router-link>\n          <span class=\"separator\">|</span>\n          <a href=\"#\" @click=\"showRoleGuide = true\" class=\"link-type\">角色说明</a>\n        </div>\n      </el-form>\n\n      <!-- 角色引导弹窗 -->\n      <el-dialog\n        title=\"角色功能说明\"\n        :visible.sync=\"showRoleGuide\"\n        width=\"500px\"\n        center\n      >\n        <div class=\"role-guide\">\n          <div class=\"guide-item\">\n            <h4><i class=\"el-icon-user-solid\"></i> 学生</h4>\n            <p>• 查看学习计划和课程内容</p>\n            <p>• 提交作业和查看成绩</p>\n            <p>• 切换学科进行学习</p>\n          </div>\n          <div class=\"guide-item\">\n            <h4><i class=\"el-icon-user\"></i> 家长</h4>\n            <p>• 关联和查看孩子学习进度</p>\n            <p>• 接收学习报告和通知</p>\n            <p>• 与老师进行沟通交流</p>\n          </div>\n          <div class=\"guide-item\">\n            <h4><i class=\"el-icon-s-custom\"></i> 教师</h4>\n            <p>• 管理班级和学生信息</p>\n            <p>• 发布课程和作业</p>\n            <p>• 查看和评估学生成绩</p>\n          </div>\n        </div>\n        <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"showRoleGuide = false\">我知道了</el-button>\n        </span>\n      </el-dialog>\n    </div>\n\n    <!-- 底部版权 -->\n    <div class=\"el-login-footer\">\n      <span>Copyright © 2018-2025 lingdu.edu All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg } from \"@/api/login\"\nimport { sendSmsCode } from \"@/api/edu\"\nimport Cookies from \"js-cookie\"\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\n\nexport default {\n  name: \"EduLogin\",\n  data() {\n    const validatePhoneNumber = (rule, value, callback) => {\n      if (!value) {\n        callback(new Error('请输入手机号'))\n      } else if (!/^1[3-9]\\d{9}$/.test(value)) {\n        callback(new Error('请输入正确的手机号'))\n      } else {\n        callback()\n      }\n    }\n    \n    return {\n      activeRole: 'student', // 当前选中的角色\n      loginType: 'password', // 登录方式：password-密码登录，sms-验证码登录\n      showRoleGuide: false, // 是否显示角色引导\n      smsCountdown: 0, // 短信倒计时\n      codeUrl: \"\",\n      loginForm: {\n        phoneNumber: \"\",\n        password: \"\",\n        smsCode: \"\",\n        rememberMe: false,\n        code: \"\",\n        uuid: \"\"\n      },\n      loginRules: {\n        phoneNumber: [\n          { required: true, trigger: \"blur\", validator: validatePhoneNumber }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\n        ],\n        smsCode: [\n          { required: true, trigger: \"blur\", message: \"请输入验证码\" },\n          { min: 6, max: 6, message: \"验证码长度为6位\", trigger: \"blur\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        this.redirect = route.query && route.query.redirect\n      },\n      immediate: true\n    },\n    loginType() {\n      // 切换登录方式时清空表单验证\n      this.$nextTick(() => {\n        this.$refs.loginForm.clearValidate()\n      })\n    }\n  },\n  created() {\n    this.getCode()\n    this.getCookie()\n  },\n  methods: {\n    // 角色切换\n    handleRoleChange(tab) {\n      this.activeRole = tab.name\n      this.$refs.loginForm.clearValidate()\n    },\n    \n    // 获取角色标题\n    getRoleTitle() {\n      const titles = {\n        student: '学生登录',\n        parent: '家长登录', \n        teacher: '教师登录'\n      }\n      return titles[this.activeRole] || '用户登录'\n    },\n    \n    // 获取注册路由\n    getRegisterRoute() {\n      return `/edu-register?role=${this.activeRole}`\n    },\n    \n    // 获取注册文本\n    getRegisterText() {\n      const texts = {\n        student: '学生注册',\n        parent: '家长注册',\n        teacher: '教师注册'\n      }\n      return texts[this.activeRole] || '立即注册'\n    },\n\n    // 获取图形验证码\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.loginForm.uuid = res.uuid\n        }\n      })\n    },\n\n    // 获取Cookie\n    getCookie() {\n      const phoneNumber = Cookies.get(\"phoneNumber\")\n      const password = Cookies.get(\"password\")\n      const rememberMe = Cookies.get('rememberMe')\n      this.loginForm = {\n        phoneNumber: phoneNumber === undefined ? this.loginForm.phoneNumber : phoneNumber,\n        password: password === undefined ? this.loginForm.password : decrypt(password),\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)\n      }\n    },\n\n    // 发送短信验证码\n    sendSmsCode() {\n      if (!this.loginForm.phoneNumber) {\n        this.$message.error('请先输入手机号')\n        return\n      }\n\n      if (!/^1[3-9]\\d{9}$/.test(this.loginForm.phoneNumber)) {\n        this.$message.error('请输入正确的手机号')\n        return\n      }\n\n      sendSmsCode(this.loginForm.phoneNumber, 'login').then(res => {\n        this.$message.success('验证码发送成功')\n        this.startCountdown()\n      }).catch(() => {\n        this.$message.error('验证码发送失败')\n      })\n    },\n\n    // 开始倒计时\n    startCountdown() {\n      this.smsCountdown = 60\n      const timer = setInterval(() => {\n        this.smsCountdown--\n        if (this.smsCountdown <= 0) {\n          clearInterval(timer)\n        }\n      }, 1000)\n    },\n\n    // 处理登录\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n\n          // 构建登录参数\n          const loginData = {\n            username: this.loginForm.phoneNumber,\n            userType: this.getUserType(),\n            loginType: this.loginType\n          }\n\n          if (this.loginType === 'password') {\n            loginData.password = this.loginForm.password\n            if (this.captchaEnabled) {\n              loginData.code = this.loginForm.code\n              loginData.uuid = this.loginForm.uuid\n            }\n          } else {\n            loginData.smsCode = this.loginForm.smsCode\n          }\n\n          // 记住密码\n          if (this.loginType === 'password' && this.loginForm.rememberMe) {\n            Cookies.set(\"phoneNumber\", this.loginForm.phoneNumber, { expires: 30 })\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 })\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })\n          } else {\n            Cookies.remove(\"phoneNumber\")\n            Cookies.remove(\"password\")\n            Cookies.remove('rememberMe')\n          }\n\n          // 调用登录接口\n          this.$store.dispatch(\"EduLogin\", loginData).then(() => {\n            this.$router.push({ path: this.redirect || this.getDefaultRoute() }).catch(()=>{})\n          }).catch(() => {\n            this.loading = false\n            if (this.loginType === 'password' && this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    },\n\n    // 获取用户类型\n    getUserType() {\n      const typeMap = {\n        student: '01',\n        parent: '02',\n        teacher: '03'\n      }\n      return typeMap[this.activeRole] || '01'\n    },\n\n    // 获取默认路由\n    getDefaultRoute() {\n      const routeMap = {\n        student: '/student',\n        parent: '/parent',\n        teacher: '/teacher'\n      }\n      return routeMap[this.activeRole] || '/'\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.edu-login {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.login-container {\n  background: #ffffff;\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  width: 450px;\n  padding: 0;\n  overflow: hidden;\n}\n\n.role-tabs {\n  .el-tabs__header {\n    margin: 0;\n    background: #f8f9fa;\n  }\n\n  .el-tabs__nav-wrap {\n    padding: 0;\n  }\n\n  .el-tabs__item {\n    height: 60px;\n    line-height: 60px;\n    font-size: 16px;\n    font-weight: 500;\n\n    &.is-active {\n      color: #409EFF;\n      background: #ffffff;\n    }\n  }\n\n  .role-icon {\n    text-align: center;\n    padding: 10px 0;\n\n    i {\n      font-size: 24px;\n      color: #909399;\n    }\n  }\n\n  .el-tabs__item.is-active .role-icon i {\n    color: #409EFF;\n  }\n}\n\n.login-form {\n  padding: 30px 40px 40px;\n\n  .title {\n    margin: 0 0 30px 0;\n    text-align: center;\n    color: #303133;\n    font-weight: bold;\n    font-size: 22px;\n  }\n\n  .login-type-switch {\n    text-align: center;\n    margin-bottom: 25px;\n  }\n\n  .el-input {\n    height: 45px;\n    input {\n      height: 45px;\n      border-radius: 6px;\n    }\n  }\n\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n\n  .sms-input-group {\n    display: flex;\n    align-items: center;\n  }\n\n  .login-code {\n    width: 35%;\n    height: 45px;\n    float: right;\n    img {\n      cursor: pointer;\n      vertical-align: middle;\n      height: 45px;\n      border-radius: 6px;\n    }\n  }\n\n  .register-links {\n    text-align: center;\n    margin-top: 20px;\n\n    .separator {\n      margin: 0 10px;\n      color: #DCDFE6;\n    }\n\n    .link-type {\n      color: #409EFF;\n      text-decoration: none;\n\n      &:hover {\n        color: #66b1ff;\n      }\n    }\n  }\n}\n\n.role-guide {\n  .guide-item {\n    margin-bottom: 20px;\n\n    h4 {\n      color: #303133;\n      margin-bottom: 10px;\n\n      i {\n        margin-right: 8px;\n        color: #409EFF;\n      }\n    }\n\n    p {\n      margin: 5px 0;\n      color: #606266;\n      padding-left: 24px;\n    }\n  }\n}\n\n.el-login-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n</style>\n"]}]}