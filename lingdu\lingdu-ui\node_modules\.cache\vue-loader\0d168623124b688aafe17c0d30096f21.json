{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\edu-register.vue", "mtime": 1758185596148}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldENvZGVJbWcgfSBmcm9tICJAL2FwaS9sb2dpbiIKaW1wb3J0IHsgcmVnaXN0ZXJTdHVkZW50LCByZWdpc3RlclRlYWNoZXIsIHJlZ2lzdGVyUGFyZW50IH0gZnJvbSAiQC9hcGkvZWR1IgoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJFZHVSZWdpc3RlciIsCiAgZGF0YSgpIHsKICAgIGNvbnN0IGVxdWFsVG9QYXNzd29yZCA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKHRoaXMucmVnaXN0ZXJGb3JtLnBhc3N3b3JkICE9PSB2YWx1ZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5Lik5qyh6L6T5YWl55qE5a+G56CB5LiN5LiA6Ie0IikpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9CgogICAgY29uc3QgdmFsaWRhdGVQaG9uZU51bWJlciA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5omL5py65Y+3JykpCiAgICAgIH0gZWxzZSBpZiAoIS9eMVszLTldXGR7OX0kLy50ZXN0KHZhbHVlKSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+3JykpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9CgogICAgY29uc3QgdmFsaWRhdGVJZENhcmQgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpei6q+S7veivgeWPtycpKQogICAgICB9IGVsc2UgaWYgKCEvXlsxLTldXGR7NX0oMTh8MTl8MjApXGR7Mn0oKDBbMS05XSl8KDFbMC0yXSkpKChbMC0yXVsxLTldKXwxMHwyMHwzMHwzMSlcZHszfVswLTlYeF0kLy50ZXN0KHZhbHVlKSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5q2j56Gu55qE6Lqr5Lu96K+B5Y+3JykpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9CgogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlUm9sZTogJ3N0dWRlbnQnLAogICAgICBjb2RlVXJsOiAiIiwKICAgICAgcmVnaXN0ZXJGb3JtOiB7CiAgICAgICAgdXNlcm5hbWU6ICIiLAogICAgICAgIHBhc3N3b3JkOiAiIiwKICAgICAgICBjb25maXJtUGFzc3dvcmQ6ICIiLAogICAgICAgIHJlYWxOYW1lOiAiIiwKICAgICAgICBjb2RlOiAiIiwKICAgICAgICB1dWlkOiAiIiwKICAgICAgICAvLyDlrabnlJ/kuJPnlKjlrZfmrrUKICAgICAgICBncmFkZUxldmVsOiAiIiwKICAgICAgICBwcmltYXJ5U3ViamVjdDogIiIsCiAgICAgICAgc2Vjb25kYXJ5U3ViamVjdHM6IFtdLAogICAgICAgIG1lbWJlclR5cGU6ICJmcmVlX3RyaWFsIiwKICAgICAgICBzY2hvb2xOYW1lOiAiIiwKICAgICAgICBjbGFzc05hbWU6ICIiLAogICAgICAgIGlkQ2FyZDogIiIsCiAgICAgICAgLy8g5pWZ5biI5LiT55So5a2X5q61CiAgICAgICAgdGVhY2hlckNlcnROdW1iZXI6ICIiLAogICAgICAgIHRlYWNoaW5nR3JhZGVMZXZlbHM6IFtdLAogICAgICAgIHRlYWNoaW5nU3ViamVjdHM6IFtdLAogICAgICAgIHRlYWNoZXJDZXJ0SW1hZ2U6ICIiLAogICAgICAgIC8vIOWutumVv+S4k+eUqOWtl+autQogICAgICAgIHBlcm1pc3Npb25MZXZlbDogInZpZXdfb25seSIKICAgICAgfSwKICAgICAgcmVnaXN0ZXJSdWxlczogewogICAgICAgIHVzZXJuYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIHZhbGlkYXRvcjogdmFsaWRhdGVQaG9uZU51bWJlciB9CiAgICAgICAgXSwKICAgICAgICBwYXNzd29yZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiLCBtZXNzYWdlOiAi6K+36L6T5YWl5a+G56CBIiB9LAogICAgICAgICAgeyBtaW46IDUsIG1heDogMjAsIG1lc3NhZ2U6ICLlr4bnoIHplb/luqblv4Xpobvku4vkuo4gNSDlkowgMjAg5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IHBhdHRlcm46IC9eW148PiInfFxcXSskLywgbWVzc2FnZTogIuS4jeiDveWMheWQq+mdnuazleWtl+espu+8mjwgPiBcIiAnIFxcXFwgfCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBjb25maXJtUGFzc3dvcmQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJibHVyIiwgbWVzc2FnZTogIuivt+WGjeasoei+k+WFpeWvhueggSIgfSwKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogZXF1YWxUb1Bhc3N3b3JkLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcmVhbE5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJibHVyIiwgbWVzc2FnZTogIuivt+i+k+WFpeecn+WunuWnk+WQjSIgfSwKICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDMwLCBtZXNzYWdlOiAi5aeT5ZCN6ZW/5bqm5b+F6aG75LuL5LqOIDIg5ZKMIDMwIOS5i+mXtCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBncmFkZUxldmVsOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiY2hhbmdlIiwgbWVzc2FnZTogIuivt+mAieaLqeWtpuautSIgfQogICAgICAgIF0sCiAgICAgICAgcHJpbWFyeVN1YmplY3Q6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36YCJ5oup5Li75a2m56eRIiB9CiAgICAgICAgXSwKICAgICAgICBpZENhcmQ6IFsKICAgICAgICAgIHsgdmFsaWRhdG9yOiB2YWxpZGF0ZUlkQ2FyZCwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHRlYWNoZXJDZXJ0TnVtYmVyOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fovpPlhaXmlZnluIjotYTmoLzor4HnvJblj7ciIH0KICAgICAgICBdLAogICAgICAgIHRlYWNoaW5nR3JhZGVMZXZlbHM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36YCJ5oup5pWZ5a2m5a2m5q61IiB9CiAgICAgICAgXSwKICAgICAgICB0ZWFjaGluZ1N1YmplY3RzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiY2hhbmdlIiwgbWVzc2FnZTogIuivt+mAieaLqeaVmeWtpuWtpuenkSIgfQogICAgICAgIF0sCiAgICAgICAgY29kZTogW3sgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36L6T5YWl6aqM6K+B56CBIiB9XQogICAgICB9LAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgY2FwdGNoYUVuYWJsZWQ6IHRydWUKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldENvZGUoKQogICAgLy8g5LuO6Lev55Sx5Y+C5pWw6I635Y+W6KeS6ImyCiAgICBpZiAodGhpcy4kcm91dGUucXVlcnkucm9sZSkgewogICAgICB0aGlzLmFjdGl2ZVJvbGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS5yb2xlCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDop5LoibLliIfmjaIKICAgIGhhbmRsZVJvbGVDaGFuZ2UodGFiKSB7CiAgICAgIHRoaXMuYWN0aXZlUm9sZSA9IHRhYi5uYW1lCiAgICAgIHRoaXMucmVzZXRGb3JtKCkKICAgIH0sCgogICAgLy8g6I635Y+W6KeS6Imy5qCH6aKYCiAgICBnZXRSb2xlVGl0bGUoKSB7CiAgICAgIGNvbnN0IHRpdGxlcyA9IHsKICAgICAgICBzdHVkZW50OiAn5a2m55Sf5rOo5YaMJywKICAgICAgICBwYXJlbnQ6ICflrrbplb/ms6jlhownLAogICAgICAgIHRlYWNoZXI6ICfmlZnluIjms6jlhownCiAgICAgIH0KICAgICAgcmV0dXJuIHRpdGxlc1t0aGlzLmFjdGl2ZVJvbGVdIHx8ICfnlKjmiLfms6jlhownCiAgICB9LAoKICAgIC8vIOiOt+WPluazqOWGjOaMiemSruaWh+acrAogICAgZ2V0UmVnaXN0ZXJCdXR0b25UZXh0KCkgewogICAgICBjb25zdCB0ZXh0cyA9IHsKICAgICAgICBzdHVkZW50OiAn5rOo5YaM5a2m55Sf6LSm5Y+3JywKICAgICAgICBwYXJlbnQ6ICfms6jlhozlrrbplb/otKblj7cnLAogICAgICAgIHRlYWNoZXI6ICfmj5DkuqTmlZnluIjorqTor4EnCiAgICAgIH0KICAgICAgcmV0dXJuIHRleHRzW3RoaXMuYWN0aXZlUm9sZV0gfHwgJ+eri+WNs+azqOWGjCcKICAgIH0sCgogICAgLy8g6I635Y+W6aqM6K+B56CBCiAgICBnZXRDb2RlKCkgewogICAgICBnZXRDb2RlSW1nKCkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuY2FwdGNoYUVuYWJsZWQgPSByZXMuY2FwdGNoYUVuYWJsZWQgPT09IHVuZGVmaW5lZCA/IHRydWUgOiByZXMuY2FwdGNoYUVuYWJsZWQKICAgICAgICBpZiAodGhpcy5jYXB0Y2hhRW5hYmxlZCkgewogICAgICAgICAgdGhpcy5jb2RlVXJsID0gImRhdGE6aW1hZ2UvZ2lmO2Jhc2U2NCwiICsgcmVzLmltZwogICAgICAgICAgdGhpcy5yZWdpc3RlckZvcm0udXVpZCA9IHJlcy51dWlkCiAgICAgICAgfQogICAgICB9KQogICAgfSwKCiAgICAvLyDlpITnkIbmlZnluIjotYTmoLzor4HkuIrkvKAKICAgIGhhbmRsZUNlcnRVcGxvYWQoZmlsZSkgewogICAgICBjb25zdCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpCiAgICAgIHJlYWRlci5vbmxvYWQgPSAoZSkgPT4gewogICAgICAgIHRoaXMucmVnaXN0ZXJGb3JtLnRlYWNoZXJDZXJ0SW1hZ2UgPSBlLnRhcmdldC5yZXN1bHQKICAgICAgfQogICAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlLnJhdykKICAgIH0sCgogICAgLy8g6YeN572u6KGo5Y2VCiAgICByZXNldEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMucmVnaXN0ZXJGb3JtLnJlc2V0RmllbGRzKCkKICAgICAgdGhpcy5yZWdpc3RlckZvcm0gPSB7CiAgICAgICAgdXNlcm5hbWU6ICIiLAogICAgICAgIHBhc3N3b3JkOiAiIiwKICAgICAgICBjb25maXJtUGFzc3dvcmQ6ICIiLAogICAgICAgIHJlYWxOYW1lOiAiIiwKICAgICAgICBjb2RlOiAiIiwKICAgICAgICB1dWlkOiB0aGlzLnJlZ2lzdGVyRm9ybS51dWlkLAogICAgICAgIGdyYWRlTGV2ZWw6ICIiLAogICAgICAgIHByaW1hcnlTdWJqZWN0OiAiIiwKICAgICAgICBzZWNvbmRhcnlTdWJqZWN0czogW10sCiAgICAgICAgbWVtYmVyVHlwZTogImZyZWVfdHJpYWwiLAogICAgICAgIHNjaG9vbE5hbWU6ICIiLAogICAgICAgIGNsYXNzTmFtZTogIiIsCiAgICAgICAgaWRDYXJkOiAiIiwKICAgICAgICB0ZWFjaGVyQ2VydE51bWJlcjogIiIsCiAgICAgICAgdGVhY2hpbmdHcmFkZUxldmVsczogW10sCiAgICAgICAgdGVhY2hpbmdTdWJqZWN0czogW10sCiAgICAgICAgdGVhY2hlckNlcnRJbWFnZTogIiIsCiAgICAgICAgcGVybWlzc2lvbkxldmVsOiAidmlld19vbmx5IgogICAgICB9CiAgICB9LAoKICAgIC8vIOWkhOeQhuazqOWGjAogICAgaGFuZGxlUmVnaXN0ZXIoKSB7CiAgICAgIHRoaXMuJHJlZnMucmVnaXN0ZXJGb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8vIOaVmeW4iOWtpuenkeaVsOmHj+mZkOWItgogICAgICAgICAgaWYgKHRoaXMuYWN0aXZlUm9sZSA9PT0gJ3RlYWNoZXInICYmIHRoaXMucmVnaXN0ZXJGb3JtLnRlYWNoaW5nU3ViamVjdHMubGVuZ3RoID4gMikgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlZnlrablrabnp5HmnIDlpJrpgInmi6ky5LiqJykKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CgogICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQoKICAgICAgICAgIC8vIOagueaNruinkuiJsuiwg+eUqOS4jeWQjOeahOazqOWGjOaOpeWPowogICAgICAgICAgbGV0IHJlZ2lzdGVyUHJvbWlzZQogICAgICAgICAgY29uc3QgZm9ybURhdGEgPSB7IC4uLnRoaXMucmVnaXN0ZXJGb3JtIH0KCiAgICAgICAgICAvLyDlpITnkIbmlbDnu4TlrZfmrrUKICAgICAgICAgIGlmIChmb3JtRGF0YS5zZWNvbmRhcnlTdWJqZWN0cyAmJiBBcnJheS5pc0FycmF5KGZvcm1EYXRhLnNlY29uZGFyeVN1YmplY3RzKSkgewogICAgICAgICAgICBmb3JtRGF0YS5zZWNvbmRhcnlTdWJqZWN0cyA9IGZvcm1EYXRhLnNlY29uZGFyeVN1YmplY3RzLmpvaW4oJywnKQogICAgICAgICAgfQogICAgICAgICAgaWYgKGZvcm1EYXRhLnRlYWNoaW5nR3JhZGVMZXZlbHMgJiYgQXJyYXkuaXNBcnJheShmb3JtRGF0YS50ZWFjaGluZ0dyYWRlTGV2ZWxzKSkgewogICAgICAgICAgICBmb3JtRGF0YS50ZWFjaGluZ0dyYWRlTGV2ZWxzID0gZm9ybURhdGEudGVhY2hpbmdHcmFkZUxldmVscy5qb2luKCcsJykKICAgICAgICAgIH0KICAgICAgICAgIGlmIChmb3JtRGF0YS50ZWFjaGluZ1N1YmplY3RzICYmIEFycmF5LmlzQXJyYXkoZm9ybURhdGEudGVhY2hpbmdTdWJqZWN0cykpIHsKICAgICAgICAgICAgZm9ybURhdGEudGVhY2hpbmdTdWJqZWN0cyA9IGZvcm1EYXRhLnRlYWNoaW5nU3ViamVjdHMuam9pbignLCcpCiAgICAgICAgICB9CgogICAgICAgICAgc3dpdGNoICh0aGlzLmFjdGl2ZVJvbGUpIHsKICAgICAgICAgICAgY2FzZSAnc3R1ZGVudCc6CiAgICAgICAgICAgICAgcmVnaXN0ZXJQcm9taXNlID0gcmVnaXN0ZXJTdHVkZW50KGZvcm1EYXRhKQogICAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICAgIGNhc2UgJ3RlYWNoZXInOgogICAgICAgICAgICAgIHJlZ2lzdGVyUHJvbWlzZSA9IHJlZ2lzdGVyVGVhY2hlcihmb3JtRGF0YSkKICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICBjYXNlICdwYXJlbnQnOgogICAgICAgICAgICAgIHJlZ2lzdGVyUHJvbWlzZSA9IHJlZ2lzdGVyUGFyZW50KGZvcm1EYXRhKQogICAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5peg5pWI55qE5rOo5YaM57G75Z6LJykKICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfQoKICAgICAgICAgIHJlZ2lzdGVyUHJvbWlzZS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgICAgICAgIGNvbnN0IHVzZXJuYW1lID0gdGhpcy5yZWdpc3RlckZvcm0udXNlcm5hbWUKICAgICAgICAgICAgbGV0IG1lc3NhZ2UgPSAnJwoKICAgICAgICAgICAgc3dpdGNoICh0aGlzLmFjdGl2ZVJvbGUpIHsKICAgICAgICAgICAgICBjYXNlICdzdHVkZW50JzoKICAgICAgICAgICAgICAgIG1lc3NhZ2UgPSBg5oGt5Zac5L2g77yM5a2m55Sf6LSm5Y+3ICR7dXNlcm5hbWV9IOazqOWGjOaIkOWKn++8gWAKICAgICAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICAgICAgY2FzZSAndGVhY2hlcic6CiAgICAgICAgICAgICAgICBtZXNzYWdlID0gYOaBreWWnOS9oO+8jOaVmeW4iOi0puWPtyAke3VzZXJuYW1lfSDms6jlhozmiJDlip/vvIHmgqjnmoTotYTmoLzmraPlnKjlrqHmoLjkuK3vvIzlrqHmoLjpgJrov4flkI7ljbPlj6/kvb/nlKjjgIJgCiAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICAgIGNhc2UgJ3BhcmVudCc6CiAgICAgICAgICAgICAgICBtZXNzYWdlID0gYOaBreWWnOS9oO+8jOWutumVv+i0puWPtyAke3VzZXJuYW1lfSDms6jlhozmiJDlip/vvIHor7fnmbvlvZXlkI7lhbPogZTmgqjnmoTlranlrZDjgIJgCiAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICB9CgogICAgICAgICAgICB0aGlzLiRhbGVydChtZXNzYWdlLCAn5rOo5YaM5oiQ5YqfJywgewogICAgICAgICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZSwKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9lZHUtbG9naW4iKQogICAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSkKICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgICAgICAgaWYgKHRoaXMuY2FwdGNoYUVuYWJsZWQpIHsKICAgICAgICAgICAgICB0aGlzLmdldENvZGUoKQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["edu-register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmSA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "edu-register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"edu-register\">\n    <div class=\"register-container\">\n      <!-- 角色选择标签页 -->\n      <el-tabs v-model=\"activeRole\" class=\"role-tabs\" @tab-click=\"handleRoleChange\">\n        <el-tab-pane label=\"学生注册\" name=\"student\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-user-solid\"></i>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"家长注册\" name=\"parent\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-user\"></i>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane label=\"教师注册\" name=\"teacher\">\n          <div class=\"role-icon\">\n            <i class=\"el-icon-s-custom\"></i>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <!-- 注册表单 -->\n      <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n        <h3 class=\"title\">{{ getRoleTitle() }}</h3>\n\n        <!-- 基础信息 -->\n        <div class=\"form-section\">\n          <h4 class=\"section-title\">基础信息</h4>\n          \n          <!-- 手机号 -->\n          <el-form-item prop=\"username\">\n            <el-input\n              v-model=\"registerForm.username\"\n              placeholder=\"手机号（用于登录）\"\n              maxlength=\"11\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"phone\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 密码 -->\n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"registerForm.password\"\n              type=\"password\"\n              placeholder=\"设置登录密码\"\n              show-password\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 确认密码 -->\n          <el-form-item prop=\"confirmPassword\">\n            <el-input\n              v-model=\"registerForm.confirmPassword\"\n              type=\"password\"\n              placeholder=\"确认登录密码\"\n              show-password\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 真实姓名 -->\n          <el-form-item prop=\"realName\">\n            <el-input\n              v-model=\"registerForm.realName\"\n              placeholder=\"真实姓名\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n        </div>\n\n        <!-- 学生专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'student'\">\n          <h4 class=\"section-title\">学习信息</h4>\n          \n          <!-- 学段选择 -->\n          <el-form-item prop=\"gradeLevel\">\n            <el-select v-model=\"registerForm.gradeLevel\" placeholder=\"选择学段\" style=\"width: 100%\">\n              <el-option-group label=\"小学\">\n                <el-option label=\"小学1年级\" value=\"primary_1\"></el-option>\n                <el-option label=\"小学2年级\" value=\"primary_2\"></el-option>\n                <el-option label=\"小学3年级\" value=\"primary_3\"></el-option>\n                <el-option label=\"小学4年级\" value=\"primary_4\"></el-option>\n                <el-option label=\"小学5年级\" value=\"primary_5\"></el-option>\n                <el-option label=\"小学6年级\" value=\"primary_6\"></el-option>\n              </el-option-group>\n              <el-option-group label=\"初中\">\n                <el-option label=\"初中1年级\" value=\"junior_1\"></el-option>\n                <el-option label=\"初中2年级\" value=\"junior_2\"></el-option>\n                <el-option label=\"初中3年级\" value=\"junior_3\"></el-option>\n              </el-option-group>\n              <el-option-group label=\"高中\">\n                <el-option label=\"高中1年级\" value=\"senior_1\"></el-option>\n                <el-option label=\"高中2年级\" value=\"senior_2\"></el-option>\n                <el-option label=\"高中3年级\" value=\"senior_3\"></el-option>\n              </el-option-group>\n            </el-select>\n          </el-form-item>\n\n          <!-- 主学科选择 -->\n          <el-form-item prop=\"primarySubject\">\n            <el-select v-model=\"registerForm.primarySubject\" placeholder=\"选择主学科（必选1个）\" style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 副学科选择 -->\n          <el-form-item>\n            <el-select v-model=\"registerForm.secondarySubjects\" placeholder=\"选择副学科（可选多个）\" multiple style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 会员类型选择 -->\n          <el-form-item>\n            <el-radio-group v-model=\"registerForm.memberType\">\n              <el-radio label=\"free_trial\">\n                <span class=\"member-option\">\n                  <strong>免费体验</strong>\n                  <small>（7天免费，限1个学科）</small>\n                </span>\n              </el-radio>\n              <el-radio label=\"basic\">\n                <span class=\"member-option\">\n                  <strong>基础会员</strong>\n                  <small>（支持多学科学习）</small>\n                </span>\n              </el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 学校信息（可选） -->\n          <el-form-item>\n            <el-input v-model=\"registerForm.schoolName\" placeholder=\"学校名称（可选）\">\n              <svg-icon slot=\"prefix\" icon-class=\"education\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n        </div>\n\n        <!-- 教师专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'teacher'\">\n          <h4 class=\"section-title\">教学信息</h4>\n          \n          <!-- 身份证号 -->\n          <el-form-item prop=\"idCard\">\n            <el-input\n              v-model=\"registerForm.idCard\"\n              placeholder=\"身份证号\"\n              maxlength=\"18\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"idcard\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教师资格证编号 -->\n          <el-form-item prop=\"teacherCertNumber\">\n            <el-input\n              v-model=\"registerForm.teacherCertNumber\"\n              placeholder=\"教师资格证编号\"\n            >\n              <svg-icon slot=\"prefix\" icon-class=\"documentation\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教学学段 -->\n          <el-form-item prop=\"teachingGradeLevels\">\n            <el-select v-model=\"registerForm.teachingGradeLevels\" placeholder=\"选择教学学段（可多选）\" multiple style=\"width: 100%\">\n              <el-option label=\"小学\" value=\"primary\"></el-option>\n              <el-option label=\"初中\" value=\"junior\"></el-option>\n              <el-option label=\"高中\" value=\"senior\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 教学学科 -->\n          <el-form-item prop=\"teachingSubjects\">\n            <el-select v-model=\"registerForm.teachingSubjects\" placeholder=\"选择教学学科（限1-2个）\" multiple style=\"width: 100%\">\n              <el-option label=\"语文\" value=\"chinese\"></el-option>\n              <el-option label=\"数学\" value=\"math\"></el-option>\n              <el-option label=\"英语\" value=\"english\"></el-option>\n              <el-option label=\"物理\" value=\"physics\"></el-option>\n              <el-option label=\"化学\" value=\"chemistry\"></el-option>\n              <el-option label=\"生物\" value=\"biology\"></el-option>\n              <el-option label=\"历史\" value=\"history\"></el-option>\n              <el-option label=\"地理\" value=\"geography\"></el-option>\n              <el-option label=\"政治\" value=\"politics\"></el-option>\n            </el-select>\n          </el-form-item>\n\n          <!-- 任职学校 -->\n          <el-form-item>\n            <el-input v-model=\"registerForm.schoolName\" placeholder=\"任职学校（可选）\">\n              <svg-icon slot=\"prefix\" icon-class=\"education\" class=\"el-input__icon input-icon\" />\n            </el-input>\n          </el-form-item>\n\n          <!-- 教师资格证上传 -->\n          <el-form-item>\n            <el-upload\n              class=\"cert-upload\"\n              drag\n              action=\"#\"\n              :auto-upload=\"false\"\n              :on-change=\"handleCertUpload\"\n              accept=\"image/*\"\n            >\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">将教师资格证照片拖到此处，或<em>点击上传</em></div>\n              <div class=\"el-upload__tip\" slot=\"tip\">只能上传jpg/png文件，且不超过2MB</div>\n            </el-upload>\n          </el-form-item>\n        </div>\n\n        <!-- 家长专用信息 -->\n        <div class=\"form-section\" v-if=\"activeRole === 'parent'\">\n          <h4 class=\"section-title\">权限设置</h4>\n          \n          <!-- 权限级别选择 -->\n          <el-form-item>\n            <el-radio-group v-model=\"registerForm.permissionLevel\">\n              <el-radio label=\"view_only\">仅查看学习进度</el-radio>\n              <el-radio label=\"view_notify\">查看进度 + 接收通知</el-radio>\n              <el-radio label=\"view_communicate\">查看进度 + 沟通老师</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </div>\n\n        <!-- 验证码 -->\n        <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n          <el-input\n            v-model=\"registerForm.code\"\n            auto-complete=\"off\"\n            placeholder=\"验证码\"\n            style=\"width: 63%\"\n          >\n            <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n          </el-input>\n          <div class=\"register-code\">\n            <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\n          </div>\n        </el-form-item>\n\n        <!-- 注册按钮 -->\n        <el-form-item style=\"width:100%;\">\n          <el-button\n            :loading=\"loading\"\n            size=\"medium\"\n            type=\"primary\"\n            style=\"width:100%;\"\n            @click.native.prevent=\"handleRegister\"\n          >\n            <span v-if=\"!loading\">{{ getRegisterButtonText() }}</span>\n            <span v-else>注 册 中...</span>\n          </el-button>\n        </el-form-item>\n\n        <!-- 登录链接 -->\n        <div class=\"login-link\">\n          <router-link class=\"link-type\" to=\"/edu-login\">已有账户？立即登录</router-link>\n        </div>\n      </el-form>\n    </div>\n\n    <!-- 底部版权 -->\n    <div class=\"el-register-footer\">\n      <span>Copyright © 2018-2025 lingdu.edu All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg } from \"@/api/login\"\nimport { registerStudent, registerTeacher, registerParent } from \"@/api/edu\"\n\nexport default {\n  name: \"EduRegister\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n\n    const validatePhoneNumber = (rule, value, callback) => {\n      if (!value) {\n        callback(new Error('请输入手机号'))\n      } else if (!/^1[3-9]\\d{9}$/.test(value)) {\n        callback(new Error('请输入正确的手机号'))\n      } else {\n        callback()\n      }\n    }\n\n    const validateIdCard = (rule, value, callback) => {\n      if (!value) {\n        callback(new Error('请输入身份证号'))\n      } else if (!/^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/.test(value)) {\n        callback(new Error('请输入正确的身份证号'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      activeRole: 'student',\n      codeUrl: \"\",\n      registerForm: {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        realName: \"\",\n        code: \"\",\n        uuid: \"\",\n        // 学生专用字段\n        gradeLevel: \"\",\n        primarySubject: \"\",\n        secondarySubjects: [],\n        memberType: \"free_trial\",\n        schoolName: \"\",\n        className: \"\",\n        idCard: \"\",\n        // 教师专用字段\n        teacherCertNumber: \"\",\n        teachingGradeLevels: [],\n        teachingSubjects: [],\n        teacherCertImage: \"\",\n        // 家长专用字段\n        permissionLevel: \"view_only\"\n      },\n      registerRules: {\n        username: [\n          { required: true, trigger: \"blur\", validator: validatePhoneNumber }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入密码\" },\n          { min: 5, max: 20, message: \"密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        realName: [\n          { required: true, trigger: \"blur\", message: \"请输入真实姓名\" },\n          { min: 2, max: 30, message: \"姓名长度必须介于 2 和 30 之间\", trigger: \"blur\" }\n        ],\n        gradeLevel: [\n          { required: true, trigger: \"change\", message: \"请选择学段\" }\n        ],\n        primarySubject: [\n          { required: true, trigger: \"change\", message: \"请选择主学科\" }\n        ],\n        idCard: [\n          { validator: validateIdCard, trigger: \"blur\" }\n        ],\n        teacherCertNumber: [\n          { required: true, trigger: \"blur\", message: \"请输入教师资格证编号\" }\n        ],\n        teachingGradeLevels: [\n          { required: true, trigger: \"change\", message: \"请选择教学学段\" }\n        ],\n        teachingSubjects: [\n          { required: true, trigger: \"change\", message: \"请选择教学学科\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n    // 从路由参数获取角色\n    if (this.$route.query.role) {\n      this.activeRole = this.$route.query.role\n    }\n  },\n  methods: {\n    // 角色切换\n    handleRoleChange(tab) {\n      this.activeRole = tab.name\n      this.resetForm()\n    },\n\n    // 获取角色标题\n    getRoleTitle() {\n      const titles = {\n        student: '学生注册',\n        parent: '家长注册',\n        teacher: '教师注册'\n      }\n      return titles[this.activeRole] || '用户注册'\n    },\n\n    // 获取注册按钮文本\n    getRegisterButtonText() {\n      const texts = {\n        student: '注册学生账号',\n        parent: '注册家长账号',\n        teacher: '提交教师认证'\n      }\n      return texts[this.activeRole] || '立即注册'\n    },\n\n    // 获取验证码\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n\n    // 处理教师资格证上传\n    handleCertUpload(file) {\n      const reader = new FileReader()\n      reader.onload = (e) => {\n        this.registerForm.teacherCertImage = e.target.result\n      }\n      reader.readAsDataURL(file.raw)\n    },\n\n    // 重置表单\n    resetForm() {\n      this.$refs.registerForm.resetFields()\n      this.registerForm = {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        realName: \"\",\n        code: \"\",\n        uuid: this.registerForm.uuid,\n        gradeLevel: \"\",\n        primarySubject: \"\",\n        secondarySubjects: [],\n        memberType: \"free_trial\",\n        schoolName: \"\",\n        className: \"\",\n        idCard: \"\",\n        teacherCertNumber: \"\",\n        teachingGradeLevels: [],\n        teachingSubjects: [],\n        teacherCertImage: \"\",\n        permissionLevel: \"view_only\"\n      }\n    },\n\n    // 处理注册\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          // 教师学科数量限制\n          if (this.activeRole === 'teacher' && this.registerForm.teachingSubjects.length > 2) {\n            this.$message.error('教学学科最多选择2个')\n            return\n          }\n\n          this.loading = true\n\n          // 根据角色调用不同的注册接口\n          let registerPromise\n          const formData = { ...this.registerForm }\n\n          // 处理数组字段\n          if (formData.secondarySubjects && Array.isArray(formData.secondarySubjects)) {\n            formData.secondarySubjects = formData.secondarySubjects.join(',')\n          }\n          if (formData.teachingGradeLevels && Array.isArray(formData.teachingGradeLevels)) {\n            formData.teachingGradeLevels = formData.teachingGradeLevels.join(',')\n          }\n          if (formData.teachingSubjects && Array.isArray(formData.teachingSubjects)) {\n            formData.teachingSubjects = formData.teachingSubjects.join(',')\n          }\n\n          switch (this.activeRole) {\n            case 'student':\n              registerPromise = registerStudent(formData)\n              break\n            case 'teacher':\n              registerPromise = registerTeacher(formData)\n              break\n            case 'parent':\n              registerPromise = registerParent(formData)\n              break\n            default:\n              this.$message.error('无效的注册类型')\n              this.loading = false\n              return\n          }\n\n          registerPromise.then(res => {\n            this.loading = false\n            const username = this.registerForm.username\n            let message = ''\n\n            switch (this.activeRole) {\n              case 'student':\n                message = `恭喜你，学生账号 ${username} 注册成功！`\n                break\n              case 'teacher':\n                message = `恭喜你，教师账号 ${username} 注册成功！您的资格正在审核中，审核通过后即可使用。`\n                break\n              case 'parent':\n                message = `恭喜你，家长账号 ${username} 注册成功！请登录后关联您的孩子。`\n                break\n            }\n\n            this.$alert(message, '注册成功', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/edu-login\")\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.edu-register {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px 0;\n}\n\n.register-container {\n  background: #ffffff;\n  border-radius: 10px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  width: 500px;\n  max-width: 90vw;\n  padding: 0;\n  overflow: hidden;\n}\n\n.role-tabs {\n  .el-tabs__header {\n    margin: 0;\n    background: #f8f9fa;\n  }\n\n  .el-tabs__nav-wrap {\n    padding: 0;\n  }\n\n  .el-tabs__item {\n    height: 60px;\n    line-height: 60px;\n    font-size: 16px;\n    font-weight: 500;\n\n    &.is-active {\n      color: #409EFF;\n      background: #ffffff;\n    }\n  }\n\n  .role-icon {\n    text-align: center;\n    padding: 10px 0;\n\n    i {\n      font-size: 24px;\n      color: #909399;\n    }\n  }\n\n  .el-tabs__item.is-active .role-icon i {\n    color: #409EFF;\n  }\n}\n\n.register-form {\n  padding: 30px 40px 40px;\n  max-height: 70vh;\n  overflow-y: auto;\n\n  .title {\n    margin: 0 0 30px 0;\n    text-align: center;\n    color: #303133;\n    font-weight: bold;\n    font-size: 22px;\n  }\n\n  .form-section {\n    margin-bottom: 25px;\n\n    .section-title {\n      color: #409EFF;\n      font-size: 16px;\n      margin-bottom: 15px;\n      padding-bottom: 8px;\n      border-bottom: 1px solid #EBEEF5;\n    }\n  }\n\n  .el-input {\n    height: 40px;\n    input {\n      height: 40px;\n      border-radius: 6px;\n    }\n  }\n\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n\n  .member-option {\n    display: block;\n\n    small {\n      display: block;\n      color: #909399;\n      font-size: 12px;\n      margin-top: 2px;\n    }\n  }\n\n  .cert-upload {\n    .el-upload {\n      width: 100%;\n    }\n\n    .el-upload-dragger {\n      width: 100%;\n      height: 120px;\n    }\n  }\n\n  .register-code {\n    width: 35%;\n    height: 40px;\n    float: right;\n    img {\n      cursor: pointer;\n      vertical-align: middle;\n      height: 40px;\n      border-radius: 6px;\n    }\n  }\n\n  .login-link {\n    text-align: center;\n    margin-top: 20px;\n\n    .link-type {\n      color: #409EFF;\n      text-decoration: none;\n\n      &:hover {\n        color: #66b1ff;\n      }\n    }\n  }\n}\n\n.el-register-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n</style>\n"]}]}