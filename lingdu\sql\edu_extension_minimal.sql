-- ===========================
-- 教育系统扩展 - 精简版
-- 基于现有RuoYi框架的最小化扩展
-- ===========================

-- ----------------------------
-- 1. 扩展字典类型
-- ----------------------------
INSERT INTO sys_dict_type VALUES(11, '用户类型', 'edu_user_type', '0', 'admin', sysdate(), '', null, '教育系统用户类型');
INSERT INTO sys_dict_type VALUES(12, '学段等级', 'edu_grade_level', '0', 'admin', sysdate(), '', null, '学段等级列表');
INSERT INTO sys_dict_type VALUES(13, '学科类型', 'edu_subject_type', '0', 'admin', sysdate(), '', null, '学科类型列表');
INSERT INTO sys_dict_type VALUES(14, '会员类型', 'edu_member_type', '0', 'admin', sysdate(), '', null, '会员类型列表');
INSERT INTO sys_dict_type VALUES(15, '审核状态', 'edu_audit_status', '0', 'admin', sysdate(), '', null, '审核状态列表');

-- ----------------------------
-- 2. 扩展字典数据
-- ----------------------------
-- 用户类型
INSERT INTO sys_dict_data VALUES(100, 1, '系统用户', '00', 'edu_user_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '系统管理用户');
INSERT INTO sys_dict_data VALUES(101, 2, '学生', '01', 'edu_user_type', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '学生用户');
INSERT INTO sys_dict_data VALUES(102, 3, '家长', '02', 'edu_user_type', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '家长用户');
INSERT INTO sys_dict_data VALUES(103, 4, '教师', '03', 'edu_user_type', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '教师用户');

-- 学段等级
INSERT INTO sys_dict_data VALUES(110, 1, '小学1年级', 'primary_1', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(111, 2, '小学2年级', 'primary_2', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(112, 3, '小学3年级', 'primary_3', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(113, 4, '小学4年级', 'primary_4', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(114, 5, '小学5年级', 'primary_5', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(115, 6, '小学6年级', 'primary_6', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(116, 7, '初中1年级', 'junior_1', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(117, 8, '初中2年级', 'junior_2', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(118, 9, '初中3年级', 'junior_3', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(119, 10, '高中1年级', 'senior_1', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(120, 11, '高中2年级', 'senior_2', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(121, 12, '高中3年级', 'senior_3', 'edu_grade_level', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');

-- 学科类型
INSERT INTO sys_dict_data VALUES(130, 1, '语文', 'chinese', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(131, 2, '数学', 'math', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(132, 3, '英语', 'english', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(133, 4, '物理', 'physics', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(134, 5, '化学', 'chemistry', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(135, 6, '生物', 'biology', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(136, 7, '历史', 'history', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(137, 8, '地理', 'geography', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(138, 9, '政治', 'politics', 'edu_subject_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '');

-- 会员类型
INSERT INTO sys_dict_data VALUES(140, 1, '免费体验', 'free_trial', 'edu_member_type', '', 'info', 'Y', '0', 'admin', sysdate(), '', null, '7天免费体验');
INSERT INTO sys_dict_data VALUES(141, 2, '基础会员', 'basic', 'edu_member_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '基础会员');
INSERT INTO sys_dict_data VALUES(142, 3, 'VIP会员', 'vip', 'edu_member_type', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, 'VIP会员');
INSERT INTO sys_dict_data VALUES(143, 4, '超级VIP', 'super_vip', 'edu_member_type', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '超级VIP会员');

-- 审核状态
INSERT INTO sys_dict_data VALUES(150, 1, '待审核', 'pending', 'edu_audit_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(151, 2, '审核通过', 'approved', 'edu_audit_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '');
INSERT INTO sys_dict_data VALUES(152, 3, '审核拒绝', 'rejected', 'edu_audit_status', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '');

-- ----------------------------
-- 3. 新增角色
-- ----------------------------
INSERT INTO sys_role VALUES('10', '学生', 'student', 10, 2, 1, 1, '0', '0', 'admin', sysdate(), '', null, '学生角色');
INSERT INTO sys_role VALUES('11', '家长', 'parent', 11, 2, 1, 1, '0', '0', 'admin', sysdate(), '', null, '家长角色');
INSERT INTO sys_role VALUES('12', '教师', 'teacher', 12, 2, 1, 1, '0', '0', 'admin', sysdate(), '', null, '教师角色');

-- ----------------------------
-- 4. 学生信息扩展表
-- ----------------------------
DROP TABLE IF EXISTS edu_student_info;
CREATE TABLE edu_student_info (
  student_id        bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '学生ID',
  user_id           bigint(20)      NOT NULL                   COMMENT '关联用户ID',
  student_number    varchar(20)     DEFAULT ''                 COMMENT '学号',
  real_name         varchar(30)     NOT NULL                   COMMENT '真实姓名',
  grade_level       varchar(20)     DEFAULT ''                 COMMENT '学段等级',
  primary_subject   varchar(20)     DEFAULT ''                 COMMENT '主学科',
  secondary_subjects varchar(200)   DEFAULT ''                 COMMENT '副学科（逗号分隔）',
  member_type       varchar(20)     DEFAULT 'free_trial'       COMMENT '会员类型',
  member_expire_date datetime                                  COMMENT '会员到期时间',
  school_name       varchar(100)    DEFAULT ''                 COMMENT '学校名称',
  class_name        varchar(50)     DEFAULT ''                 COMMENT '班级名称',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime                                   COMMENT '创建时间',
  update_by         varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       datetime                                   COMMENT '更新时间',
  PRIMARY KEY (student_id),
  UNIQUE KEY uk_user_id (user_id)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '学生信息表';

-- ----------------------------
-- 5. 教师信息扩展表
-- ----------------------------
DROP TABLE IF EXISTS edu_teacher_info;
CREATE TABLE edu_teacher_info (
  teacher_id            bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '教师ID',
  user_id               bigint(20)      NOT NULL                   COMMENT '关联用户ID',
  real_name             varchar(30)     NOT NULL                   COMMENT '真实姓名',
  id_card               varchar(18)     DEFAULT ''                 COMMENT '身份证号',
  teacher_cert_number   varchar(50)     DEFAULT ''                 COMMENT '教师资格证编号',
  teaching_grade_levels varchar(100)    DEFAULT ''                 COMMENT '教学学段（逗号分隔）',
  teaching_subjects     varchar(100)    DEFAULT ''                 COMMENT '教学学科（逗号分隔）',
  school_name           varchar(100)    DEFAULT ''                 COMMENT '任职学校',
  cert_image_url        varchar(200)    DEFAULT ''                 COMMENT '教师资格证图片',
  audit_status          varchar(20)     DEFAULT 'pending'          COMMENT '审核状态',
  audit_remark          varchar(500)    DEFAULT ''                 COMMENT '审核备注',
  create_by             varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time           datetime                                   COMMENT '创建时间',
  update_by             varchar(64)     DEFAULT ''                 COMMENT '更新者',
  update_time           datetime                                   COMMENT '更新时间',
  PRIMARY KEY (teacher_id),
  UNIQUE KEY uk_user_id (user_id)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '教师信息表';

-- ----------------------------
-- 6. 家长学生关联表
-- ----------------------------
DROP TABLE IF EXISTS edu_parent_student;
CREATE TABLE edu_parent_student (
  relation_id       bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '关联ID',
  parent_user_id    bigint(20)      NOT NULL                   COMMENT '家长用户ID',
  student_user_id   bigint(20)      NOT NULL                   COMMENT '学生用户ID',
  relation_type     varchar(20)     DEFAULT 'parent'           COMMENT '关系类型（parent-父母，guardian-监护人）',
  permission_level  varchar(20)     DEFAULT 'view_only'        COMMENT '权限级别',
  bind_time         datetime                                   COMMENT '绑定时间',
  create_by         varchar(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       datetime                                   COMMENT '创建时间',
  PRIMARY KEY (relation_id),
  UNIQUE KEY uk_parent_student (parent_user_id, student_user_id)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '家长学生关联表';

-- ----------------------------
-- 7. 短信验证码表
-- ----------------------------
DROP TABLE IF EXISTS edu_sms_verify;
CREATE TABLE edu_sms_verify (
  verify_id         bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '验证ID',
  phone_number      varchar(11)     NOT NULL                   COMMENT '手机号',
  verify_code       varchar(6)      NOT NULL                   COMMENT '验证码',
  verify_type       varchar(20)     NOT NULL                   COMMENT '验证类型（register-注册，login-登录，bind-绑定）',
  expire_time       datetime        NOT NULL                   COMMENT '过期时间',
  used_flag         char(1)         DEFAULT '0'                COMMENT '使用标志（0-未使用，1-已使用）',
  create_time       datetime                                   COMMENT '创建时间',
  PRIMARY KEY (verify_id),
  KEY idx_phone_type (phone_number, verify_type)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '短信验证码表';

-- ----------------------------
-- 8. 用户角色切换记录表（可选）
-- ----------------------------
DROP TABLE IF EXISTS edu_user_role_switch;
CREATE TABLE edu_user_role_switch (
  switch_id         bigint(20)      NOT NULL AUTO_INCREMENT    COMMENT '切换ID',
  user_id           bigint(20)      NOT NULL                   COMMENT '用户ID',
  from_role_type    varchar(2)      DEFAULT ''                 COMMENT '切换前角色',
  to_role_type      varchar(2)      NOT NULL                   COMMENT '切换后角色',
  context_data      varchar(500)    DEFAULT ''                 COMMENT '上下文数据',
  switch_time       datetime                                   COMMENT '切换时间',
  PRIMARY KEY (switch_id),
  KEY idx_user_id (user_id)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT = '用户角色切换记录表';
