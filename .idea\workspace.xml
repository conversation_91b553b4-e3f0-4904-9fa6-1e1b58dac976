<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="450647cf-2c30-4e0f-b86a-7bf4f3634c32" name="更改" comment="调整基础模块家长的字段" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="32jFA5N5EjQr6c2qnHCDUSBU5ZG" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.RuoYiApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\IntelliJIdea2025.2\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true",
    "数据库脚本.quartz.sql.executor": "Run",
    "数据库脚本.ry_20250522.sql.executor": "Run"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ],
    "RunConfigurationTargetLRU": [
      "d8617a0f-1412-4d4c-929a-1f965d4f7d64/schema/\"ruoyi\"",
      "d8617a0f-1412-4d4c-929a-1f965d4f7d64",
      "c3a2f638-3ae7-4140-a711-a1dbc93b1664"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="lingdu-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lingdu.RuoYiApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.lingdu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-bf35d07a577b-intellij.indexing.shared.core-IU-252.25557.131" />
        <option value="bundled-js-predefined-d6986cc7102b-b598e85cdad2-JavaScript-IU-252.25557.131" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="450647cf-2c30-4e0f-b86a-7bf4f3634c32" name="更改" comment="" />
      <created>1757925931189</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757925931189</updated>
      <workItem from="1757925933072" duration="525000" />
      <workItem from="1757926474842" duration="1686000" />
      <workItem from="1757983923326" duration="7678000" />
      <workItem from="1758090891878" duration="9396000" />
      <workItem from="1758156279470" duration="5288000" />
    </task>
    <task id="LOCAL-00001" summary="初始化灵渡">
      <option name="closed" value="true" />
      <created>1757986568534</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1757986568534</updated>
    </task>
    <task id="LOCAL-00002" summary="初始化灵渡">
      <option name="closed" value="true" />
      <created>1757988550410</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1757988550410</updated>
    </task>
    <task id="LOCAL-00003" summary="学生信息初始化">
      <option name="closed" value="true" />
      <created>1758094949472</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1758094949472</updated>
    </task>
    <task id="LOCAL-00004" summary="完善用户与角色模块">
      <option name="closed" value="true" />
      <created>1758098399070</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1758098399070</updated>
    </task>
    <task id="LOCAL-00005" summary="调整基础模块的字段">
      <option name="closed" value="true" />
      <created>1758178434830</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1758178434830</updated>
    </task>
    <task id="LOCAL-00006" summary="调整基础模块家长的字段">
      <option name="closed" value="true" />
      <created>1758181097315</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1758181097315</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化灵渡" />
    <MESSAGE value="学生信息初始化" />
    <MESSAGE value="完善用户与角色模块" />
    <MESSAGE value="调整基础模块的字段" />
    <MESSAGE value="调整基础模块家长的字段" />
    <option name="LAST_COMMIT_MESSAGE" value="调整基础模块家长的字段" />
  </component>
</project>