<template>
  <div class="edu-login">
    <div class="login-container">
      <!-- 角色选择标签页 -->
      <el-tabs v-model="activeRole" class="role-tabs" @tab-click="handleRoleChange">
        <el-tab-pane label="学生登录" name="student">
          <div class="role-icon">
            <i class="el-icon-user-solid"></i>
          </div>
        </el-tab-pane>
        <el-tab-pane label="家长登录" name="parent">
          <div class="role-icon">
            <i class="el-icon-user"></i>
          </div>
        </el-tab-pane>
        <el-tab-pane label="教师登录" name="teacher">
          <div class="role-icon">
            <i class="el-icon-s-custom"></i>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 登录表单 -->
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="title">{{ getRoleTitle() }}</h3>
        
        <!-- 登录方式切换 -->
        <div class="login-type-switch">
          <el-radio-group v-model="loginType" size="small">
            <el-radio-button label="password">密码登录</el-radio-button>
            <el-radio-button label="sms">验证码登录</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 手机号输入 -->
        <el-form-item prop="phoneNumber">
          <el-input
            v-model="loginForm.phoneNumber"
            type="text"
            auto-complete="off"
            placeholder="请输入手机号"
            maxlength="11"
          >
            <svg-icon slot="prefix" icon-class="phone" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>

        <!-- 密码输入（密码登录时显示） -->
        <el-form-item prop="password" v-if="loginType === 'password'">
          <el-input
            v-model="loginForm.password"
            type="password"
            auto-complete="off"
            placeholder="请输入密码"
            @keyup.enter.native="handleLogin"
          >
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>

        <!-- 短信验证码输入（验证码登录时显示） -->
        <el-form-item prop="smsCode" v-if="loginType === 'sms'">
          <div class="sms-input-group">
            <el-input
              v-model="loginForm.smsCode"
              auto-complete="off"
              placeholder="请输入验证码"
              maxlength="6"
              style="width: 60%"
              @keyup.enter.native="handleLogin"
            >
              <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
            </el-input>
            <el-button 
              type="primary" 
              :disabled="smsCountdown > 0" 
              @click="sendSmsCode"
              style="width: 38%; margin-left: 2%"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>

        <!-- 图形验证码（密码登录时显示） -->
        <el-form-item prop="code" v-if="loginType === 'password' && captchaEnabled">
          <el-input
            v-model="loginForm.code"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter.native="handleLogin"
          >
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img"/>
          </div>
        </el-form-item>

        <!-- 记住密码 -->
        <el-checkbox v-model="loginForm.rememberMe" v-if="loginType === 'password'" style="margin:0px 0px 25px 0px;">
          记住密码
        </el-checkbox>

        <!-- 登录按钮 -->
        <el-form-item style="width:100%;">
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            style="width:100%;"
            @click.native.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>

        <!-- 注册链接 -->
        <div class="register-links">
          <router-link class="link-type" :to="getRegisterRoute()">
            {{ getRegisterText() }}
          </router-link>
          <span class="separator">|</span>
          <a href="#" @click="showRoleGuide = true" class="link-type">角色说明</a>
        </div>
      </el-form>

      <!-- 角色引导弹窗 -->
      <el-dialog
        title="角色功能说明"
        :visible.sync="showRoleGuide"
        width="500px"
        center
      >
        <div class="role-guide">
          <div class="guide-item">
            <h4><i class="el-icon-user-solid"></i> 学生</h4>
            <p>• 查看学习计划和课程内容</p>
            <p>• 提交作业和查看成绩</p>
            <p>• 切换学科进行学习</p>
          </div>
          <div class="guide-item">
            <h4><i class="el-icon-user"></i> 家长</h4>
            <p>• 关联和查看孩子学习进度</p>
            <p>• 接收学习报告和通知</p>
            <p>• 与老师进行沟通交流</p>
          </div>
          <div class="guide-item">
            <h4><i class="el-icon-s-custom"></i> 教师</h4>
            <p>• 管理班级和学生信息</p>
            <p>• 发布课程和作业</p>
            <p>• 查看和评估学生成绩</p>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="showRoleGuide = false">我知道了</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 底部版权 -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2025 lingdu.edu All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login"
import { sendSmsCode } from "@/api/edu"
import Cookies from "js-cookie"
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "EduLogin",
  data() {
    const validatePhoneNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号'))
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }
    
    return {
      activeRole: 'student', // 当前选中的角色
      loginType: 'password', // 登录方式：password-密码登录，sms-验证码登录
      showRoleGuide: false, // 是否显示角色引导
      smsCountdown: 0, // 短信倒计时
      codeUrl: "",
      loginForm: {
        phoneNumber: "",
        password: "",
        smsCode: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        phoneNumber: [
          { required: true, trigger: "blur", validator: validatePhoneNumber }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        smsCode: [
          { required: true, trigger: "blur", message: "请输入验证码" },
          { min: 6, max: 6, message: "验证码长度为6位", trigger: "blur" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      captchaEnabled: true
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    },
    loginType() {
      // 切换登录方式时清空表单验证
      this.$nextTick(() => {
        this.$refs.loginForm.clearValidate()
      })
    }
  },
  created() {
    this.getCode()
    this.getCookie()
  },
  methods: {
    // 角色切换
    handleRoleChange(tab) {
      this.activeRole = tab.name
      this.$refs.loginForm.clearValidate()
    },
    
    // 获取角色标题
    getRoleTitle() {
      const titles = {
        student: '学生登录',
        parent: '家长登录', 
        teacher: '教师登录'
      }
      return titles[this.activeRole] || '用户登录'
    },
    
    // 获取注册路由
    getRegisterRoute() {
      return `/edu-register?role=${this.activeRole}`
    },
    
    // 获取注册文本
    getRegisterText() {
      const texts = {
        student: '学生注册',
        parent: '家长注册',
        teacher: '教师注册'
      }
      return texts[this.activeRole] || '立即注册'
    },

    // 获取图形验证码
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img
          this.loginForm.uuid = res.uuid
        }
      })
    },

    // 获取Cookie
    getCookie() {
      const phoneNumber = Cookies.get("phoneNumber")
      const password = Cookies.get("password")
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        phoneNumber: phoneNumber === undefined ? this.loginForm.phoneNumber : phoneNumber,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      }
    },

    // 发送短信验证码
    sendSmsCode() {
      if (!this.loginForm.phoneNumber) {
        this.$message.error('请先输入手机号')
        return
      }

      if (!/^1[3-9]\d{9}$/.test(this.loginForm.phoneNumber)) {
        this.$message.error('请输入正确的手机号')
        return
      }

      sendSmsCode(this.loginForm.phoneNumber, 'login').then(res => {
        this.$message.success('验证码发送成功')
        this.startCountdown()
      }).catch(() => {
        this.$message.error('验证码发送失败')
      })
    },

    // 开始倒计时
    startCountdown() {
      this.smsCountdown = 60
      const timer = setInterval(() => {
        this.smsCountdown--
        if (this.smsCountdown <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    },

    // 处理登录
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true

          // 构建登录参数
          const loginData = {
            username: this.loginForm.phoneNumber,
            userType: this.getUserType(),
            loginType: this.loginType
          }

          if (this.loginType === 'password') {
            loginData.password = this.loginForm.password
            if (this.captchaEnabled) {
              loginData.code = this.loginForm.code
              loginData.uuid = this.loginForm.uuid
            }
          } else {
            loginData.smsCode = this.loginForm.smsCode
          }

          // 记住密码
          if (this.loginType === 'password' && this.loginForm.rememberMe) {
            Cookies.set("phoneNumber", this.loginForm.phoneNumber, { expires: 30 })
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 })
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })
          } else {
            Cookies.remove("phoneNumber")
            Cookies.remove("password")
            Cookies.remove('rememberMe')
          }

          // 调用登录接口
          this.$store.dispatch("EduLogin", loginData).then(() => {
            this.$router.push({ path: this.redirect || this.getDefaultRoute() }).catch(()=>{})
          }).catch(() => {
            this.loading = false
            if (this.loginType === 'password' && this.captchaEnabled) {
              this.getCode()
            }
          })
        }
      })
    },

    // 获取用户类型
    getUserType() {
      const typeMap = {
        student: '01',
        parent: '02',
        teacher: '03'
      }
      return typeMap[this.activeRole] || '01'
    },

    // 获取默认路由
    getDefaultRoute() {
      const routeMap = {
        student: '/student',
        parent: '/parent',
        teacher: '/teacher'
      }
      return routeMap[this.activeRole] || '/'
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.edu-login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 450px;
  padding: 0;
  overflow: hidden;
}

.role-tabs {
  .el-tabs__header {
    margin: 0;
    background: #f8f9fa;
  }

  .el-tabs__nav-wrap {
    padding: 0;
  }

  .el-tabs__item {
    height: 60px;
    line-height: 60px;
    font-size: 16px;
    font-weight: 500;

    &.is-active {
      color: #409EFF;
      background: #ffffff;
    }
  }

  .role-icon {
    text-align: center;
    padding: 10px 0;

    i {
      font-size: 24px;
      color: #909399;
    }
  }

  .el-tabs__item.is-active .role-icon i {
    color: #409EFF;
  }
}

.login-form {
  padding: 30px 40px 40px;

  .title {
    margin: 0 0 30px 0;
    text-align: center;
    color: #303133;
    font-weight: bold;
    font-size: 22px;
  }

  .login-type-switch {
    text-align: center;
    margin-bottom: 25px;
  }

  .el-input {
    height: 45px;
    input {
      height: 45px;
      border-radius: 6px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }

  .sms-input-group {
    display: flex;
    align-items: center;
  }

  .login-code {
    width: 35%;
    height: 45px;
    float: right;
    img {
      cursor: pointer;
      vertical-align: middle;
      height: 45px;
      border-radius: 6px;
    }
  }

  .register-links {
    text-align: center;
    margin-top: 20px;

    .separator {
      margin: 0 10px;
      color: #DCDFE6;
    }

    .link-type {
      color: #409EFF;
      text-decoration: none;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

.role-guide {
  .guide-item {
    margin-bottom: 20px;

    h4 {
      color: #303133;
      margin-bottom: 10px;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }

    p {
      margin: 5px 0;
      color: #606266;
      padding-left: 24px;
    }
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
