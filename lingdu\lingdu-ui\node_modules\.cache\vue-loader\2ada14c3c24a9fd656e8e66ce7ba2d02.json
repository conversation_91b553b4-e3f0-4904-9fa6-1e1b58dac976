{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\index.vue?vue&type=style&index=0&id=a83bd3b0&scoped=true&lang=css", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\index.vue", "mtime": 1758178462825}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757926224263}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757926225750}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757926224811}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkKA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div class=\"ai-learning-page\">\r\n        <el-header class=\"header\" height=\"64px\">\r\n            <el-row justify=\"center\">\r\n                <el-col :span=\"24\">\r\n                    <div class=\"section-header\">\r\n                        <h2 class=\"section-title\">\r\n                            <i class=\"el-icon-reading\"></i>\r\n                            选择学习科目\r\n                        </h2>\r\n                        <p class=\"section-subtitle\">选择您想要学习的科目，开始您的AI学习之旅</p>\r\n                    </div>\r\n                </el-col>\r\n            </el-row>\r\n        </el-header>\r\n        <!-- 主要内容区域 -->\r\n        <el-main class=\"main-content\">\r\n            <el-container class=\"content-container\">\r\n\r\n\r\n                <!-- 学科卡片网格 -->\r\n                <el-row :gutter=\"24\" class=\"subjects-row\">\r\n                    <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\" v-for=\"(subject, index) in subjects\" :key=\"index\"\r\n                        class=\"subject-col\">\r\n                        <el-card :body-style=\"{ padding: '32px 20px' }\" class=\"subject-card\" shadow=\"hover\"\r\n                            @click.native=\"goToStudy(subject)\">\r\n                            <div class=\"subject-content\">\r\n                                <div class=\"subject-icon\">\r\n                                    <i :class=\"subject.icon\"></i>\r\n                                </div>\r\n                                <div class=\"subject-name\">{{ subject.name }}</div>\r\n                                <div class=\"subject-description\">{{ subject.description }}</div>\r\n                                <el-button type=\"text\" class=\"study-btn\">\r\n                                    开始学习\r\n                                </el-button>\r\n                            </div>\r\n                        </el-card>\r\n                    </el-col>\r\n                </el-row>\r\n            </el-container>\r\n        </el-main>\r\n\r\n        <site-footer />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport SiteFooter from '@/components/SiteFooter'\r\nexport default {\r\n    name: 'AILearningPage',\r\n    components: { SiteFooter },\r\n    data() {\r\n        return {\r\n            activeIndex: 'home',\r\n            loginLoading: false,\r\n            registerLoading: false,\r\n            subjects: [\r\n                {\r\n                    name: '小学数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '启蒙阶段，重视兴趣培养与基础能力：识字、拼音、四则运算、科学常识等。课程注重体验式学习和良好习惯形成。',\r\n                    link: '/primary',\r\n                    color: '#409EFF'\r\n                },\r\n                {\r\n                    name: '初中数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '启蒙阶段，重视兴趣培养与基础能力：识字、拼音、四则运算、科学常识等。课程注重体验式学习和良好习惯形成。',\r\n                    link: '/primary',\r\n                    color: '#409EFF'\r\n                },\r\n                {\r\n                    name: '高中数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '系统夯实学科基础：语数英全面提升，物化生引入概念与实验探究，历史地理形成时空观念与区域认知。',\r\n                    link: '/junior',\r\n                    color: '#67C23A'\r\n                },\r\n                {\r\n                    name: '小学英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '系统夯实学科基础：语数英全面提升，物化生引入概念与实验探究，历史地理形成时空观念与区域认知。',\r\n                    link: '/junior',\r\n                    color: '#67C23A'\r\n                },\r\n                {\r\n                    name: '初中英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '学科深化与思维拔高：函数与几何、物理建模、化学反应机理、生物遗传与生态、文学鉴赏与写作、英语高阶应用。',\r\n                    link: '/senior',\r\n                    color: '#E6A23C'\r\n                },\r\n                {\r\n                    name: '高中英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '学科深化与思维拔高：函数与几何、物理建模、化学反应机理、生物遗传与生态、文学鉴赏与写作、英语高阶应用。',\r\n                    link: '/senior',\r\n                    color: '#E6A23C'\r\n                },\r\n            ]\r\n        }\r\n    },\r\n    methods: {\r\n        goToStudy(subject) {\r\n            this.$message({\r\n                message: `正在进入${subject.name}学习页面...`,\r\n                type: 'success',\r\n                duration: 1500\r\n            });\r\n\r\n            // 模拟加载延迟\r\n            setTimeout(() => {\r\n                this.$router.push(subject.link);\r\n            }, 500);\r\n        },\r\n\r\n        handleLogin() {\r\n            this.loginLoading = true;\r\n            setTimeout(() => {\r\n                this.loginLoading = false;\r\n                this.$router.push('/login');\r\n            }, 1000);\r\n        },\r\n\r\n        handleRegister() {\r\n            this.registerLoading = true;\r\n            setTimeout(() => {\r\n                this.registerLoading = false;\r\n                this.$router.push('/register');\r\n            }, 1000);\r\n        },\r\n\r\n        handleSelect(key) {\r\n            this.activeIndex = key;\r\n            switch (key) {\r\n                case 'home':\r\n                    this.$message('您已在首页');\r\n                    break;\r\n                case 'about':\r\n                    this.$message('关于我们页面开发中...');\r\n                    break;\r\n                case 'help':\r\n                    this.$message('帮助页面开发中...');\r\n                    break;\r\n            }\r\n        }\r\n    },\r\n\r\n    mounted() {\r\n        // 页面加载完成后的初始化\r\n        this.$nextTick(() => {\r\n            this.$message({\r\n                message: '欢迎使用灵渡AI学习助手！',\r\n                type: 'success',\r\n                duration: 3000\r\n            });\r\n        });\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.ai-learning-page {\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n/* 头部样式 */\r\n.header {\r\n    margin-top: 20px;\r\n}\r\n\r\n.header-row {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 20px;\r\n}\r\n\r\n.logo-section {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.logo {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border-radius: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n    font-size: 20px;\r\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n    margin: 0;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.nav-col {\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.nav-menu {\r\n    border-bottom: none;\r\n    background: transparent;\r\n}\r\n\r\n.nav-menu .el-menu-item {\r\n    color: #606266;\r\n    font-weight: 500;\r\n    border-bottom: 2px solid transparent;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.nav-menu .el-menu-item:hover,\r\n.nav-menu .el-menu-item.is-active {\r\n    color: #409EFF;\r\n    border-bottom-color: #409EFF;\r\n    background: transparent;\r\n}\r\n\r\n.auth-col {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n}\r\n\r\n.auth-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n}\r\n\r\n.login-btn {\r\n    border: 1px solid #409EFF;\r\n    color: #409EFF;\r\n    background: transparent;\r\n    border-radius: 6px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.login-btn:hover {\r\n    background-color: #409EFF;\r\n    color: white;\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n    padding: 60px 0;\r\n    background: transparent;\r\n    flex: 1;\r\n}\r\n\r\n.content-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 60px 20px;\r\n}\r\n\r\n.section-header {\r\n    text-align: center;\r\n    margin-bottom: 60px;\r\n}\r\n\r\n.section-title {\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n    color: #303133;\r\n    margin: 0 0 16px 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 12px;\r\n}\r\n\r\n.section-title i {\r\n    color: #409EFF;\r\n    font-size: 32px;\r\n}\r\n\r\n.section-subtitle {\r\n    font-size: 16px;\r\n    color: #909399;\r\n    margin: 0;\r\n    font-weight: 400;\r\n}\r\n\r\n/* 学科卡片网格 */\r\n.subjects-row {\r\n    margin-top: 5px;\r\n}\r\n\r\n.subject-col {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.subject-card {\r\n    border-radius: 16px;\r\n    border: 1px solid #e4e7ed;\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    height: 250px;\r\n}\r\n\r\n/* 让卡片内容填满高度并垂直居中，保证每张卡片视觉高度一致 */\r\n.subject-card .el-card__body {\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.subject-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 4px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    transform: scaleX(0);\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.subject-card:hover::before {\r\n    transform: scaleX(1);\r\n}\r\n\r\n.subject-card:hover {\r\n    transform: translateY(-8px);\r\n    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\r\n    border-color: #409EFF;\r\n}\r\n\r\n.subject-content {\r\n    text-align: center;\r\n    position: relative;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 0px;\r\n}\r\n\r\n.subject-icon {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.subject-icon i {\r\n    font-size: 48px;\r\n    color: #409EFF;\r\n    display: block;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.subject-card:hover .subject-icon i {\r\n    transform: scale(1.1);\r\n    color: #667eea;\r\n}\r\n\r\n.subject-name {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.subject-description {\r\n    font-size: 14px;\r\n    color: #909399;\r\n    margin-bottom: 20px;\r\n    line-height: 1.5;\r\n    text-align: center;\r\n    max-width: 90%;\r\n    display: -webkit-box;\r\n    line-clamp: 2;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.study-btn {\r\n    color: #409EFF;\r\n    font-weight: 500;\r\n    padding: 8px 16px;\r\n    border-radius: 6px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.study-btn:hover {\r\n    background-color: #ecf5ff;\r\n    color: #409EFF;\r\n}\r\n\r\n/* 底部样式 */\r\n.footer {\r\n    background: rgba(255, 255, 255, 0.8);\r\n    backdrop-filter: blur(10px);\r\n    border-top: 1px solid #e4e7ed;\r\n    margin-top: auto;\r\n}\r\n\r\n.footer-content {\r\n    text-align: center;\r\n}\r\n\r\n.footer-content p {\r\n    color: #909399;\r\n    font-size: 14px;\r\n    margin: 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .header-row {\r\n        padding: 0 16px;\r\n    }\r\n\r\n    .title {\r\n        font-size: 18px;\r\n    }\r\n\r\n    .nav-col {\r\n        display: none;\r\n    }\r\n\r\n    .auth-col {\r\n        justify-content: center;\r\n    }\r\n\r\n    .auth-buttons {\r\n        gap: 8px;\r\n    }\r\n\r\n    .section-title {\r\n        font-size: 28px;\r\n        flex-direction: column;\r\n        gap: 8px;\r\n    }\r\n\r\n    .section-subtitle {\r\n        font-size: 14px;\r\n    }\r\n\r\n    .subjects-row {\r\n        margin-top: 30px;\r\n    }\r\n\r\n    .subject-card {\r\n        margin-bottom: 16px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .main-content {\r\n        padding: 40px 0;\r\n    }\r\n\r\n    .section-title {\r\n        font-size: 24px;\r\n    }\r\n\r\n    .subject-icon i {\r\n        font-size: 36px;\r\n    }\r\n\r\n    .subject-name {\r\n        font-size: 18px;\r\n    }\r\n}\r\n\r\n/* Element UI 组件样式覆盖 */\r\n.el-header {\r\n    padding: 0;\r\n}\r\n\r\n.el-main {\r\n    padding: 0;\r\n}\r\n\r\n.el-footer {\r\n    padding: 0;\r\n}\r\n\r\n.el-card {\r\n    border: none;\r\n}\r\n\r\n.el-card:hover {\r\n    border-color: #409EFF;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n.subject-card {\r\n    animation: fadeInUp 0.6s ease-out;\r\n}\r\n\r\n.subject-card:nth-child(1) {\r\n    animation-delay: 0.1s;\r\n}\r\n\r\n.subject-card:nth-child(2) {\r\n    animation-delay: 0.2s;\r\n}\r\n\r\n.subject-card:nth-child(3) {\r\n    animation-delay: 0.3s;\r\n}\r\n\r\n.subject-card:nth-child(4) {\r\n    animation-delay: 0.4s;\r\n}\r\n\r\n.subject-card:nth-child(5) {\r\n    animation-delay: 0.5s;\r\n}\r\n\r\n.subject-card:nth-child(6) {\r\n    animation-delay: 0.6s;\r\n}\r\n\r\n.subject-card:nth-child(7) {\r\n    animation-delay: 0.7s;\r\n}\r\n\r\n.subject-card:nth-child(8) {\r\n    animation-delay: 0.8s;\r\n}\r\n</style>"]}]}