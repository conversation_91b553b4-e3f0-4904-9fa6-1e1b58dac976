{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\index.vue", "mtime": 1758178462825}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1757925919067}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757926224815}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757926223772}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757926225240}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_SiteFooter", "_interopRequireDefault", "require", "name", "components", "SiteFooter", "data", "activeIndex", "loginLoading", "registerLoading", "subjects", "icon", "description", "link", "color", "methods", "goToStudy", "subject", "_this", "$message", "message", "concat", "type", "duration", "setTimeout", "$router", "push", "handleLogin", "_this2", "handleRegister", "_this3", "handleSelect", "key", "mounted", "_this4", "$nextTick"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"ai-learning-page\">\r\n        <el-header class=\"header\" height=\"64px\">\r\n            <el-row justify=\"center\">\r\n                <el-col :span=\"24\">\r\n                    <div class=\"section-header\">\r\n                        <h2 class=\"section-title\">\r\n                            <i class=\"el-icon-reading\"></i>\r\n                            选择学习科目\r\n                        </h2>\r\n                        <p class=\"section-subtitle\">选择您想要学习的科目，开始您的AI学习之旅</p>\r\n                    </div>\r\n                </el-col>\r\n            </el-row>\r\n        </el-header>\r\n        <!-- 主要内容区域 -->\r\n        <el-main class=\"main-content\">\r\n            <el-container class=\"content-container\">\r\n\r\n\r\n                <!-- 学科卡片网格 -->\r\n                <el-row :gutter=\"24\" class=\"subjects-row\">\r\n                    <el-col :xs=\"12\" :sm=\"8\" :md=\"8\" :lg=\"8\" :xl=\"8\" v-for=\"(subject, index) in subjects\" :key=\"index\"\r\n                        class=\"subject-col\">\r\n                        <el-card :body-style=\"{ padding: '32px 20px' }\" class=\"subject-card\" shadow=\"hover\"\r\n                            @click.native=\"goToStudy(subject)\">\r\n                            <div class=\"subject-content\">\r\n                                <div class=\"subject-icon\">\r\n                                    <i :class=\"subject.icon\"></i>\r\n                                </div>\r\n                                <div class=\"subject-name\">{{ subject.name }}</div>\r\n                                <div class=\"subject-description\">{{ subject.description }}</div>\r\n                                <el-button type=\"text\" class=\"study-btn\">\r\n                                    开始学习\r\n                                </el-button>\r\n                            </div>\r\n                        </el-card>\r\n                    </el-col>\r\n                </el-row>\r\n            </el-container>\r\n        </el-main>\r\n\r\n        <site-footer />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport SiteFooter from '@/components/SiteFooter'\r\nexport default {\r\n    name: 'AILearningPage',\r\n    components: { SiteFooter },\r\n    data() {\r\n        return {\r\n            activeIndex: 'home',\r\n            loginLoading: false,\r\n            registerLoading: false,\r\n            subjects: [\r\n                {\r\n                    name: '小学数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '启蒙阶段，重视兴趣培养与基础能力：识字、拼音、四则运算、科学常识等。课程注重体验式学习和良好习惯形成。',\r\n                    link: '/primary',\r\n                    color: '#409EFF'\r\n                },\r\n                {\r\n                    name: '初中数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '启蒙阶段，重视兴趣培养与基础能力：识字、拼音、四则运算、科学常识等。课程注重体验式学习和良好习惯形成。',\r\n                    link: '/primary',\r\n                    color: '#409EFF'\r\n                },\r\n                {\r\n                    name: '高中数学',\r\n                    icon: 'el-icon-reading',\r\n                    description: '系统夯实学科基础：语数英全面提升，物化生引入概念与实验探究，历史地理形成时空观念与区域认知。',\r\n                    link: '/junior',\r\n                    color: '#67C23A'\r\n                },\r\n                {\r\n                    name: '小学英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '系统夯实学科基础：语数英全面提升，物化生引入概念与实验探究，历史地理形成时空观念与区域认知。',\r\n                    link: '/junior',\r\n                    color: '#67C23A'\r\n                },\r\n                {\r\n                    name: '初中英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '学科深化与思维拔高：函数与几何、物理建模、化学反应机理、生物遗传与生态、文学鉴赏与写作、英语高阶应用。',\r\n                    link: '/senior',\r\n                    color: '#E6A23C'\r\n                },\r\n                {\r\n                    name: '高中英语',\r\n                    icon: 'el-icon-s-management',\r\n                    description: '学科深化与思维拔高：函数与几何、物理建模、化学反应机理、生物遗传与生态、文学鉴赏与写作、英语高阶应用。',\r\n                    link: '/senior',\r\n                    color: '#E6A23C'\r\n                },\r\n            ]\r\n        }\r\n    },\r\n    methods: {\r\n        goToStudy(subject) {\r\n            this.$message({\r\n                message: `正在进入${subject.name}学习页面...`,\r\n                type: 'success',\r\n                duration: 1500\r\n            });\r\n\r\n            // 模拟加载延迟\r\n            setTimeout(() => {\r\n                this.$router.push(subject.link);\r\n            }, 500);\r\n        },\r\n\r\n        handleLogin() {\r\n            this.loginLoading = true;\r\n            setTimeout(() => {\r\n                this.loginLoading = false;\r\n                this.$router.push('/login');\r\n            }, 1000);\r\n        },\r\n\r\n        handleRegister() {\r\n            this.registerLoading = true;\r\n            setTimeout(() => {\r\n                this.registerLoading = false;\r\n                this.$router.push('/register');\r\n            }, 1000);\r\n        },\r\n\r\n        handleSelect(key) {\r\n            this.activeIndex = key;\r\n            switch (key) {\r\n                case 'home':\r\n                    this.$message('您已在首页');\r\n                    break;\r\n                case 'about':\r\n                    this.$message('关于我们页面开发中...');\r\n                    break;\r\n                case 'help':\r\n                    this.$message('帮助页面开发中...');\r\n                    break;\r\n            }\r\n        }\r\n    },\r\n\r\n    mounted() {\r\n        // 页面加载完成后的初始化\r\n        this.$nextTick(() => {\r\n            this.$message({\r\n                message: '欢迎使用灵渡AI学习助手！',\r\n                type: 'success',\r\n                duration: 3000\r\n            });\r\n        });\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.ai-learning-page {\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n/* 头部样式 */\r\n.header {\r\n    margin-top: 20px;\r\n}\r\n\r\n.header-row {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 20px;\r\n}\r\n\r\n.logo-section {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.logo {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border-radius: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n    font-size: 20px;\r\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.title {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n    margin: 0;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.nav-col {\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.nav-menu {\r\n    border-bottom: none;\r\n    background: transparent;\r\n}\r\n\r\n.nav-menu .el-menu-item {\r\n    color: #606266;\r\n    font-weight: 500;\r\n    border-bottom: 2px solid transparent;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.nav-menu .el-menu-item:hover,\r\n.nav-menu .el-menu-item.is-active {\r\n    color: #409EFF;\r\n    border-bottom-color: #409EFF;\r\n    background: transparent;\r\n}\r\n\r\n.auth-col {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n}\r\n\r\n.auth-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n}\r\n\r\n.login-btn {\r\n    border: 1px solid #409EFF;\r\n    color: #409EFF;\r\n    background: transparent;\r\n    border-radius: 6px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.login-btn:hover {\r\n    background-color: #409EFF;\r\n    color: white;\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n    padding: 60px 0;\r\n    background: transparent;\r\n    flex: 1;\r\n}\r\n\r\n.content-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 60px 20px;\r\n}\r\n\r\n.section-header {\r\n    text-align: center;\r\n    margin-bottom: 60px;\r\n}\r\n\r\n.section-title {\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n    color: #303133;\r\n    margin: 0 0 16px 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 12px;\r\n}\r\n\r\n.section-title i {\r\n    color: #409EFF;\r\n    font-size: 32px;\r\n}\r\n\r\n.section-subtitle {\r\n    font-size: 16px;\r\n    color: #909399;\r\n    margin: 0;\r\n    font-weight: 400;\r\n}\r\n\r\n/* 学科卡片网格 */\r\n.subjects-row {\r\n    margin-top: 5px;\r\n}\r\n\r\n.subject-col {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.subject-card {\r\n    border-radius: 16px;\r\n    border: 1px solid #e4e7ed;\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    height: 250px;\r\n}\r\n\r\n/* 让卡片内容填满高度并垂直居中，保证每张卡片视觉高度一致 */\r\n.subject-card .el-card__body {\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.subject-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 4px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    transform: scaleX(0);\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.subject-card:hover::before {\r\n    transform: scaleX(1);\r\n}\r\n\r\n.subject-card:hover {\r\n    transform: translateY(-8px);\r\n    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\r\n    border-color: #409EFF;\r\n}\r\n\r\n.subject-content {\r\n    text-align: center;\r\n    position: relative;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 0px;\r\n}\r\n\r\n.subject-icon {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.subject-icon i {\r\n    font-size: 48px;\r\n    color: #409EFF;\r\n    display: block;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.subject-card:hover .subject-icon i {\r\n    transform: scale(1.1);\r\n    color: #667eea;\r\n}\r\n\r\n.subject-name {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.subject-description {\r\n    font-size: 14px;\r\n    color: #909399;\r\n    margin-bottom: 20px;\r\n    line-height: 1.5;\r\n    text-align: center;\r\n    max-width: 90%;\r\n    display: -webkit-box;\r\n    line-clamp: 2;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.study-btn {\r\n    color: #409EFF;\r\n    font-weight: 500;\r\n    padding: 8px 16px;\r\n    border-radius: 6px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.study-btn:hover {\r\n    background-color: #ecf5ff;\r\n    color: #409EFF;\r\n}\r\n\r\n/* 底部样式 */\r\n.footer {\r\n    background: rgba(255, 255, 255, 0.8);\r\n    backdrop-filter: blur(10px);\r\n    border-top: 1px solid #e4e7ed;\r\n    margin-top: auto;\r\n}\r\n\r\n.footer-content {\r\n    text-align: center;\r\n}\r\n\r\n.footer-content p {\r\n    color: #909399;\r\n    font-size: 14px;\r\n    margin: 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .header-row {\r\n        padding: 0 16px;\r\n    }\r\n\r\n    .title {\r\n        font-size: 18px;\r\n    }\r\n\r\n    .nav-col {\r\n        display: none;\r\n    }\r\n\r\n    .auth-col {\r\n        justify-content: center;\r\n    }\r\n\r\n    .auth-buttons {\r\n        gap: 8px;\r\n    }\r\n\r\n    .section-title {\r\n        font-size: 28px;\r\n        flex-direction: column;\r\n        gap: 8px;\r\n    }\r\n\r\n    .section-subtitle {\r\n        font-size: 14px;\r\n    }\r\n\r\n    .subjects-row {\r\n        margin-top: 30px;\r\n    }\r\n\r\n    .subject-card {\r\n        margin-bottom: 16px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .main-content {\r\n        padding: 40px 0;\r\n    }\r\n\r\n    .section-title {\r\n        font-size: 24px;\r\n    }\r\n\r\n    .subject-icon i {\r\n        font-size: 36px;\r\n    }\r\n\r\n    .subject-name {\r\n        font-size: 18px;\r\n    }\r\n}\r\n\r\n/* Element UI 组件样式覆盖 */\r\n.el-header {\r\n    padding: 0;\r\n}\r\n\r\n.el-main {\r\n    padding: 0;\r\n}\r\n\r\n.el-footer {\r\n    padding: 0;\r\n}\r\n\r\n.el-card {\r\n    border: none;\r\n}\r\n\r\n.el-card:hover {\r\n    border-color: #409EFF;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n.subject-card {\r\n    animation: fadeInUp 0.6s ease-out;\r\n}\r\n\r\n.subject-card:nth-child(1) {\r\n    animation-delay: 0.1s;\r\n}\r\n\r\n.subject-card:nth-child(2) {\r\n    animation-delay: 0.2s;\r\n}\r\n\r\n.subject-card:nth-child(3) {\r\n    animation-delay: 0.3s;\r\n}\r\n\r\n.subject-card:nth-child(4) {\r\n    animation-delay: 0.4s;\r\n}\r\n\r\n.subject-card:nth-child(5) {\r\n    animation-delay: 0.5s;\r\n}\r\n\r\n.subject-card:nth-child(6) {\r\n    animation-delay: 0.6s;\r\n}\r\n\r\n.subject-card:nth-child(7) {\r\n    animation-delay: 0.7s;\r\n}\r\n\r\n.subject-card:nth-child(8) {\r\n    animation-delay: 0.8s;\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;AA+CA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,YAAA;MACAC,eAAA;MACAC,QAAA,GACA;QACAP,IAAA;QACAQ,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAX,IAAA;QACAQ,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAX,IAAA;QACAQ,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAX,IAAA;QACAQ,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAX,IAAA;QACAQ,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAX,IAAA;QACAQ,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA;IAEA;EACA;EACAC,OAAA;IACAC,SAAA,WAAAA,UAAAC,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,QAAA;QACAC,OAAA,6BAAAC,MAAA,CAAAJ,OAAA,CAAAd,IAAA;QACAmB,IAAA;QACAC,QAAA;MACA;;MAEA;MACAC,UAAA;QACAN,KAAA,CAAAO,OAAA,CAAAC,IAAA,CAAAT,OAAA,CAAAJ,IAAA;MACA;IACA;IAEAc,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAApB,YAAA;MACAgB,UAAA;QACAI,MAAA,CAAApB,YAAA;QACAoB,MAAA,CAAAH,OAAA,CAAAC,IAAA;MACA;IACA;IAEAG,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAArB,eAAA;MACAe,UAAA;QACAM,MAAA,CAAArB,eAAA;QACAqB,MAAA,CAAAL,OAAA,CAAAC,IAAA;MACA;IACA;IAEAK,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAzB,WAAA,GAAAyB,GAAA;MACA,QAAAA,GAAA;QACA;UACA,KAAAb,QAAA;UACA;QACA;UACA,KAAAA,QAAA;UACA;QACA;UACA,KAAAA,QAAA;UACA;MACA;IACA;EACA;EAEAc,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,SAAA;MACAD,MAAA,CAAAf,QAAA;QACAC,OAAA;QACAE,IAAA;QACAC,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}