<template>
  <!-- 头部导航 -->
  <el-header class="header" height="64px">
    <el-row type="flex" justify="space-between" align="middle" class="header-row">
      <!-- Logo和标题 -->
      <el-col :span="6">
        <div class="logo-section">
          <div class="logo">
            <i class="el-icon-cpu"></i>
          </div>
          <h1 class="title">灵渡AI学习助手</h1>
        </div>
      </el-col>

      <!-- 导航链接 -->
      <el-col :span="12" class="nav-col">
        <el-menu mode="horizontal" :default-active="activeIndex" class="nav-menu" @select="handleSelect">
          <el-menu-item index="home">首页</el-menu-item>
          <el-menu-item index="about">关于我们</el-menu-item>
          <el-menu-item index="help">帮助</el-menu-item>
        </el-menu>
      </el-col>

      <!-- 登录注册 / 退出 -->
      <el-col :span="6" class="auth-col">
        <div class="auth-buttons">
          <el-button v-if="isLoggedIn" @click="goProfile">
            个人中心
          </el-button>
          <el-button v-if="!isLoggedIn" class="login-btn" @click="handleLogin" :loading="loginLoading">
            登录
          </el-button>
          <el-button v-if="!isLoggedIn" type="primary" @click="handleRegister" :loading="registerLoading">
            注册
          </el-button>
          <el-button v-else type="danger" @click="logout">
            退出
          </el-button>
        </div>
      </el-col>
    </el-row>
  </el-header>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import { getToken } from '@/utils/auth'

export default {
  emits: ['setLayout'],
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc
  },
  data() {
    return {
      activeIndex: 'home',
      loginLoading: false,
      registerLoading: false,
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
      'nickName'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    },
    isLoggedIn() {
      return !!getToken()
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    setLayout(event) {
      this.$emit('setLayout')
    },
    handleSelect(key) {
      this.activeIndex = key
      switch (key) {
        case 'home':
          this.$router.push('/')
          break
        case 'about':
          this.$router.push('/about')
          break
        case 'help':
          // 如果存在帮助路由则跳转，否则给出提示
          const hasHelp = this.$router.options.routes.some(r => r.path === '/help')
          if (hasHelp) {
            this.$router.push('/help')
          } else {
            this.$message('帮助页面开发中...')
          }
          break
      }
    },
    handleLogin() {
      this.loginLoading = true
      const hasToken = this.$store.getters.token || this.$store.getters.roles?.length > 0
      const goLogin = () => {
        this.loginLoading = false
        this.$router.replace('/login')
      }
      if (hasToken) {
        // 清除登录态再进入登录页，避免被路由守卫重定向回首页
        this.$store.dispatch('LogOut').finally(goLogin)
      } else {
        goLogin()
      }
    },
    handleRegister() {
      this.registerLoading = true
      setTimeout(() => {
        this.registerLoading = false
        this.$router.push('/register')
      }, 1000)
    },
    logout() {
      this.$confirm('确定退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index'
        })
      }).catch(() => { })
    },
    goProfile() {
      this.$router.push('/user/profile')
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 0px;
      padding-right: 0px;

      .avatar-wrapper {
        margin-top: 10px;
        right: 8px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 30px;
          height: 30px;
          border-radius: 50%;
        }

        .user-nickname {
          position: relative;
          bottom: 10px;
          left: 2px;
          font-size: 14px;
          font-weight: bold;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>

<style scoped>
/* Header navbar styles */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid #e4e7ed;
}

.header-row {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-col {
  display: flex;
  justify-content: center;
}

.nav-menu {
  border-bottom: none;
  background: transparent;
}

.nav-menu .el-menu-item {
  color: #606266;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.nav-menu .el-menu-item:hover,
.nav-menu .el-menu-item.is-active {
  color: #409EFF;
  border-bottom-color: #409EFF;
  background: transparent;
}

.auth-col {
  display: flex;
  justify-content: flex-end;
}

.auth-buttons {
  display: flex;
  gap: 12px;
}

.login-btn {
  border: 1px solid #409EFF;
  color: #409EFF;
  background: transparent;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background-color: #409EFF;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}
</style>
